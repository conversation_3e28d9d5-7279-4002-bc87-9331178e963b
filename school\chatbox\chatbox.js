/**
 * Modern Chatbox System
 * Features: Real-time messaging, emojis, file attachments, typing indicators
 */

class ModernChatbox {
    constructor() {
        this.currentUserId = null;
        this.currentUserImage = 'images/profile/thumbs/profile.png'; // Default image
        this.currentRecipientId = null;
        this.currentGroupId = null;
        this.currentGroupForMembers = null;
        this.currentChatType = 'direct'; // 'direct' or 'group'
        this.lastMessageId = 0;
        this.pollInterval = null;
        this.typingTimeout = null;
        this.isTyping = false;

        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.loadEmojis();
        this.getCurrentUser();
        this.startPolling();
        this.updateOnlineStatus('online');
        
        // Load initial data
        this.loadConversations();
        this.loadUsers();
        this.loadGroups();
    }
    
    setupEventListeners() {
        // Toggle chat
        document.getElementById('chatToggle').addEventListener('click', () => {
            this.toggleChat();
        });
        
        document.getElementById('closeChat').addEventListener('click', () => {
            this.closeChat();
        });
        
        // Tab switching
        document.querySelectorAll('.chat-tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                this.switchTab(e.target.dataset.tab);
            });
        });
        
        // Back to chats
        document.getElementById('backToChats').addEventListener('click', () => {
            this.showConversations();
        });

        // Group functionality
        document.getElementById('createGroupBtn')?.addEventListener('click', () => {
            this.showCreateGroupModal();
        });

        document.getElementById('createGroupSubmit')?.addEventListener('click', () => {
            this.createGroup();
        });

        // Add members search functionality
        document.getElementById('memberSearch')?.addEventListener('input', (e) => {
            this.searchUsersForGroup(e.target.value);
        });
        
        // Message input
        const messageInput = document.getElementById('messageInput');
        messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });
        
        messageInput.addEventListener('input', () => {
            this.handleTyping();
        });
        
        // Send button
        document.getElementById('sendBtn').addEventListener('click', () => {
            this.sendMessage();
        });
        
        // Emoji button
        document.getElementById('emojiBtn').addEventListener('click', () => {
            this.toggleEmojiPicker();
        });
        
        // File button
        document.getElementById('fileBtn').addEventListener('click', () => {
            document.getElementById('fileInput').click();
        });
        
        // File input
        document.getElementById('fileInput').addEventListener('change', (e) => {
            this.handleFileSelect(e.target.files[0]);
        });
        
        // User search
        document.getElementById('userSearch').addEventListener('input', (e) => {
            this.searchUsers(e.target.value);
        });
        
        // Close emoji picker when clicking outside
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.emoji-picker') && !e.target.closest('#emojiBtn')) {
                document.getElementById('emojiPicker').style.display = 'none';
            }
        });
        
        // Update online status before page unload
        window.addEventListener('beforeunload', () => {
            this.updateOnlineStatus('offline');
        });
        
        // Handle visibility change
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.updateOnlineStatus('away');
            } else {
                this.updateOnlineStatus('online');
            }
        });
    }
    
    async getCurrentUser() {
        // Get current user ID and profile image from session or API
        try {
            const response = await fetch('chatbox/api.php?action=get_current_user');
            const data = await response.json();
            if (data.success) {
                this.currentUserId = data.user_id;
                this.currentUserImage = data.image;
            }
        } catch (error) {
            console.error('Error getting current user:', error);
            // Fallback: try to get from PHP session
            this.currentUserId = window.currentUserId || 1; // Default fallback
            this.currentUserImage = 'images/profile/thumbs/profile.png';
        }
    }
    
    toggleChat() {
        const container = document.getElementById('chatContainer');
        const toggle = document.getElementById('chatToggle');
        
        if (container.classList.contains('open')) {
            this.closeChat();
        } else {
            container.classList.add('open');
            toggle.style.display = 'none';
            this.loadConversations();
        }
    }
    
    closeChat() {
        const container = document.getElementById('chatContainer');
        const toggle = document.getElementById('chatToggle');
        
        container.classList.remove('open');
        toggle.style.display = 'block';
    }
    
    switchTab(tabName) {
        // Update tab buttons
        document.querySelectorAll('.chat-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
        
        // Update tab content
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        
        if (tabName === 'conversations') {
            document.getElementById('conversationsTab').classList.add('active');
            this.loadConversations();
        } else if (tabName === 'users') {
            document.getElementById('usersTab').classList.add('active');
            this.loadUsers();
        } else if (tabName === 'groups') {
            document.getElementById('groupsTab').classList.add('active');
            this.loadGroups();
        }
    }
    
    showConversations() {
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById('conversationsTab').classList.add('active');
        
        // Update tab button
        document.querySelectorAll('.chat-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector('[data-tab="conversations"]').classList.add('active');
        
        document.getElementById('chatTitle').textContent = 'Messages';
    }
    
    async loadConversations() {
        try {
            const response = await fetch('chatbox/api.php?action=get_conversations');
            const data = await response.json();

            console.log('Conversations loaded:', data);
            if (data.success) {
                this.renderConversations(data.conversations);
            }
        } catch (error) {
            console.error('Error loading conversations:', error);
        }
    }
    
    renderConversations(conversations) {
        const container = document.getElementById('conversationsList');
        
        if (conversations.length === 0) {
            container.innerHTML = `
                <div class="text-center text-muted p-3">
                    <i class="fas fa-comments fa-2x mb-2"></i>
                    <p>No conversations yet</p>
                    <small>Start a new conversation from the Users tab</small>
                </div>
            `;
            return;
        }
        
        container.innerHTML = conversations.map(conv => `
            <div class="conversation-item" data-user-id="${conv.user_id}" onclick="chatbox.openChat(${conv.user_id}, '${conv.user_name}', '${conv.user_image}')">
                <div style="position: relative;">
                    <img src="${conv.user_image}" alt="${conv.user_name}" class="user-avatar" onerror="this.src='images/profile/thumbs/profile.png'">
                    <div class="online-status ${conv.online_status}"></div>
                </div>
                <div class="user-info">
                    <div class="user-name">${conv.user_name}</div>
                    <div class="last-message">${this.formatMessage(conv.last_message, conv.last_message_type)}</div>
                </div>
                ${conv.unread_count > 0 ? `<div class="unread-badge">${conv.unread_count}</div>` : ''}
            </div>
        `).join('');
    }
    
    async loadUsers(search = '') {
        try {
            const url = `chatbox/api.php?action=get_users${search ? `&search=${encodeURIComponent(search)}` : ''}`;
            const response = await fetch(url);
            const data = await response.json();
            
            if (data.success) {
                this.renderUsers(data.users);
            }
        } catch (error) {
            console.error('Error loading users:', error);
        }
    }
    
    renderUsers(users) {
        const container = document.getElementById('usersList');
        
        if (users.length === 0) {
            container.innerHTML = `
                <div class="text-center text-muted p-3">
                    <i class="fas fa-users fa-2x mb-2"></i>
                    <p>No users found</p>
                </div>
            `;
            return;
        }
        
        container.innerHTML = users.map(user => `
            <div class="user-item" onclick="chatbox.openChat(${user.id}, '${user.name}', '${user.image}')">
                <div style="position: relative;">
                    <img src="${user.image}" alt="${user.name}" class="user-avatar" onerror="this.src='images/profile/thumbs/profile.png'">
                    <div class="online-status ${user.online_status}"></div>
                </div>
                <div class="user-info">
                    <div class="user-name">${user.name}</div>
                    <div class="last-message">${user.position || 'Employee'}</div>
                </div>
            </div>
        `).join('');
    }
    
    searchUsers(query) {
        clearTimeout(this.searchTimeout);
        this.searchTimeout = setTimeout(() => {
            this.loadUsers(query);
        }, 300);
    }

    async openChat(userId, userName, userImage) {
        console.log('Opening chat with user:', userId, userName);
        this.currentRecipientId = userId;

        // Show messages tab
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById('messagesTab').classList.add('active');

        // Update chat header
        document.getElementById('chatTitle').textContent = userName;
        document.getElementById('chatUserName').textContent = userName;
        document.getElementById('chatUserAvatar').src = userImage;

        // Load messages
        await this.loadMessages();

        // Mark as read
        console.log('Marking messages as read for user:', userId);
        await this.markAsRead(userId);

        // Refresh conversations to update unread counts
        console.log('Refreshing conversations...');
        setTimeout(() => {
            this.loadConversations();
        }, 500); // Small delay to ensure database updates are complete

        // Scroll to bottom
        this.scrollToBottom();
    }

    async loadMessages() {
        if (!this.currentRecipientId) return;

        try {
            const response = await fetch(`chatbox/api.php?action=get_messages&recipient_id=${this.currentRecipientId}`);
            const data = await response.json();

            if (data.success) {
                this.renderMessages(data.messages);
                if (data.messages.length > 0) {
                    this.lastMessageId = Math.max(...data.messages.map(m => m.id));
                }
            }
        } catch (error) {
            console.error('Error loading messages:', error);
        }
    }

    renderMessages(messages) {
        const container = document.getElementById('chatMessages');

        if (messages.length === 0) {
            container.innerHTML = `
                <div class="text-center text-muted p-3">
                    <i class="fas fa-comment fa-2x mb-2"></i>
                    <p>No messages yet</p>
                    <small>Send a message to start the conversation</small>
                </div>
            `;
            return;
        }

        container.innerHTML = messages.map(msg => this.renderMessage(msg)).join('');
        this.scrollToBottom();
    }

    renderMessage(message) {
        const isOwn = message.sender_id == this.currentUserId;
        const time = this.formatTime(message.created_at);

        console.log('Rendering message:', {
            isOwn: isOwn,
            sender_id: message.sender_id,
            currentUserId: this.currentUserId,
            sender_image: message.sender_image
        });

        let content = '';
        if (message.message_type === 'text' || message.message_type === 'emoji') {
            content = this.escapeHtml(message.message);
        } else if (message.message_type === 'image') {
            // Add cache busting for images
            const imageUrl = message.file_path + '?t=' + Date.now();
            content = `
                <img src="${imageUrl}" alt="Image" class="file-preview" onclick="window.open('${message.file_path}', '_blank')"
                     onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                <div style="display:none;" class="file-attachment d-flex align-items-center">
                    <div class="file-icon image">
                        <i class="fa fa-file-image-o"></i>
                    </div>
                    <div class="file-info">
                        <a href="${message.file_path}" target="_blank" class="file-name">
                            ${this.escapeHtml(message.file_name || 'Image')}
                        </a>
                        <div class="file-size">Image file</div>
                    </div>
                </div>
                <div class="mt-1">${this.escapeHtml(message.message)}</div>
            `;
        } else if (message.message_type === 'file') {
            const fileInfo = this.getFileTypeInfo(message.file_name);
            content = `
                <div class="file-attachment d-flex align-items-center">
                    <div class="file-icon ${fileInfo.type}">
                        <i class="fa ${fileInfo.icon}"></i>
                    </div>
                    <div class="file-info">
                        <a href="${message.file_path}" target="_blank" class="file-name">
                            ${this.escapeHtml(message.file_name)}
                        </a>
                        <div class="file-size">${this.formatFileSize(message.file_size)}</div>
                    </div>
                </div>
            `;
        }

        return `
            <div class="message ${isOwn ? 'own' : ''}" data-message-id="${message.id}">
                <img src="${message.sender_image}" alt="${message.sender_name}" class="user-avatar" onerror="this.src='images/profile/thumbs/profile.png'">
                <div class="message-content">
                    ${content}
                    <div class="message-time">${time}</div>
                </div>
            </div>
        `;
    }

    async sendMessage() {
        const input = document.getElementById('messageInput');
        const message = input.value.trim();

        if (!message) return;

        // Check if we're in a group chat or direct chat
        if (this.currentChatType === 'group' && this.currentGroupId) {
            await this.sendGroupMessage(message);
        } else if (this.currentRecipientId) {
            await this.sendDirectMessage(message);
        }

        input.value = '';
        this.stopTyping();
    }

    async sendDirectMessage(message) {
        try {
            const response = await fetch('chatbox/api.php?action=send_message', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    recipient_id: this.currentRecipientId,
                    message: message,
                    message_type: 'text'
                })
            });

            const data = await response.json();

            if (data.success) {
                // Add message to UI immediately
                const tempMessage = {
                    id: data.message_id,
                    sender_id: this.currentUserId,
                    recipient_id: this.currentRecipientId,
                    message: message,
                    message_type: 'text',
                    created_at: data.timestamp,
                    sender_name: 'You',
                    sender_image: this.currentUserImage
                };

                this.addMessageToUI(tempMessage);
                this.lastMessageId = data.message_id;

                // Refresh conversations
                this.loadConversations();
            }
        } catch (error) {
            console.error('Error sending direct message:', error);
        }
    }

    async sendGroupMessage(message) {
        try {
            console.log('Sending group message:', {
                group_id: this.currentGroupId,
                message: message,
                user_id: this.currentUserId
            });

            const response = await fetch('chatbox/api.php?action=send_group_message', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `group_id=${this.currentGroupId}&message=${encodeURIComponent(message)}&message_type=text`
            });

            console.log('Response status:', response.status);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            console.log('Group message response:', data);

            if (data.success) {
                // Add message to UI immediately
                const tempMessage = {
                    id: data.message_id,
                    sender_id: this.currentUserId,
                    group_id: this.currentGroupId,
                    message: message,
                    message_type: 'text',
                    created_at: data.timestamp,
                    sender_name: 'You',
                    sender_image: this.currentUserImage
                };

                this.addMessageToUI(tempMessage);
                this.lastMessageId = data.message_id;

                // Refresh groups
                this.loadGroups();
            } else {
                console.error('Failed to send group message:', data.error);
                alert('Failed to send message: ' + data.error);
            }
        } catch (error) {
            console.error('Error sending group message:', error);
            alert('Failed to send message: ' + error.message);
        }
    }

    addMessageToUI(message) {
        const container = document.getElementById('chatMessages');

        // Remove "no messages" placeholder if exists
        const placeholder = container.querySelector('.text-center');
        if (placeholder) {
            placeholder.remove();
        }

        // Add new message
        const messageHtml = this.renderMessage(message);
        container.insertAdjacentHTML('beforeend', messageHtml);

        this.scrollToBottom();
    }

    async handleFileSelect(file) {
        if (!file || !this.currentRecipientId) return;

        // Check file size (max 10MB)
        if (file.size > 10 * 1024 * 1024) {
            alert('File size must be less than 10MB');
            return;
        }

        const formData = new FormData();
        formData.append('file', file);
        formData.append('recipient_id', this.currentRecipientId);

        try {
            const response = await fetch('chatbox/api.php?action=upload_file', {
                method: 'POST',
                body: formData
            });

            const data = await response.json();

            if (data.success) {
                // Add file message to UI
                const tempMessage = {
                    id: data.message_id,
                    sender_id: this.currentUserId,
                    recipient_id: this.currentRecipientId,
                    message: data.message_type === 'image' ? '📷 Image' : '📎 ' + data.file_name,
                    message_type: data.message_type,
                    file_path: data.file_path,
                    file_name: data.file_name,
                    file_size: file.size,
                    created_at: new Date().toISOString(),
                    sender_name: 'You',
                    sender_image: this.currentUserImage
                };

                this.addMessageToUI(tempMessage);
                this.lastMessageId = data.message_id;

                // Refresh conversations
                this.loadConversations();
            }
        } catch (error) {
            console.error('Error uploading file:', error);
        }

        // Reset file input
        document.getElementById('fileInput').value = '';
    }

    loadEmojis() {
        const emojis = [
            '😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣',
            '😊', '😇', '🙂', '🙃', '😉', '😌', '😍', '🥰',
            '😘', '😗', '😙', '😚', '😋', '😛', '😝', '😜',
            '🤪', '🤨', '🧐', '🤓', '😎', '🤩', '🥳', '😏',
            '😒', '😞', '😔', '😟', '😕', '🙁', '☹️', '😣',
            '😖', '😫', '😩', '🥺', '😢', '😭', '😤', '😠',
            '😡', '🤬', '🤯', '😳', '🥵', '🥶', '😱', '😨',
            '😰', '😥', '😓', '🤗', '🤔', '🤭', '🤫', '🤥',
            '😶', '😐', '😑', '😬', '🙄', '😯', '😦', '😧',
            '😮', '😲', '🥱', '😴', '🤤', '😪', '😵', '🤐',
            '🥴', '🤢', '🤮', '🤧', '😷', '🤒', '🤕', '🤑',
            '🤠', '😈', '👿', '👹', '👺', '🤡', '💩', '👻',
            '💀', '☠️', '👽', '👾', '🤖', '🎃', '😺', '😸',
            '😹', '😻', '😼', '😽', '🙀', '😿', '😾', '❤️',
            '🧡', '💛', '💚', '💙', '💜', '🖤', '🤍', '🤎',
            '💔', '❣️', '💕', '💞', '💓', '💗', '💖', '💘',
            '💝', '💟', '👍', '👎', '👌', '🤌', '🤏', '✌️',
            '🤞', '🤟', '🤘', '🤙', '👈', '👉', '👆', '🖕',
            '👇', '☝️', '👋', '🤚', '🖐️', '✋', '🖖', '👏',
            '🙌', '🤝', '🙏', '✍️', '💪', '🦾', '🦿', '🦵'
        ];

        const emojiGrid = document.getElementById('emojiGrid');
        emojiGrid.innerHTML = emojis.map(emoji =>
            `<div class="emoji-item" onclick="chatbox.insertEmoji('${emoji}')">${emoji}</div>`
        ).join('');
    }

    toggleEmojiPicker() {
        const picker = document.getElementById('emojiPicker');
        picker.style.display = picker.style.display === 'none' ? 'block' : 'none';
    }

    insertEmoji(emoji) {
        const input = document.getElementById('messageInput');
        const start = input.selectionStart;
        const end = input.selectionEnd;
        const text = input.value;

        input.value = text.substring(0, start) + emoji + text.substring(end);
        input.selectionStart = input.selectionEnd = start + emoji.length;
        input.focus();

        // Hide emoji picker
        document.getElementById('emojiPicker').style.display = 'none';
    }

    handleTyping() {
        if (!this.currentRecipientId) return;

        if (!this.isTyping) {
            this.isTyping = true;
            this.setTypingStatus(true);
        }

        clearTimeout(this.typingTimeout);
        this.typingTimeout = setTimeout(() => {
            this.stopTyping();
        }, 2000);
    }

    stopTyping() {
        if (this.isTyping) {
            this.isTyping = false;
            this.setTypingStatus(false);
        }
        clearTimeout(this.typingTimeout);
    }

    async setTypingStatus(isTyping) {
        if (!this.currentRecipientId) return;

        try {
            await fetch('chatbox/api.php?action=set_typing', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `recipient_id=${this.currentRecipientId}&is_typing=${isTyping ? 1 : 0}`
            });
        } catch (error) {
            console.error('Error setting typing status:', error);
        }
    }

    async markAsRead(senderId) {
        try {
            console.log('Sending mark_read request for sender:', senderId);
            const response = await fetch('chatbox/api.php?action=mark_read', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `sender_id=${senderId}`
            });

            const data = await response.json();
            console.log('Mark read response:', data);
            if (data.success) {
                console.log('Messages marked as read successfully');
            } else {
                console.error('Failed to mark as read:', data.error);
            }
        } catch (error) {
            console.error('Error marking as read:', error);
        }
    }

    async updateOnlineStatus(status) {
        try {
            await fetch('chatbox/api.php?action=update_status', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `status=${status}`
            });
        } catch (error) {
            console.error('Error updating status:', error);
        }
    }

    startPolling() {
        // Poll for new messages every 2 seconds
        this.pollInterval = setInterval(() => {
            this.pollForNewMessages();
        }, 2000);
    }

    async pollForNewMessages() {
        try {
            const response = await fetch(`chatbox/api.php?action=get_new_messages&last_message_id=${this.lastMessageId}`);
            const data = await response.json();

            if (data.success && data.messages.length > 0) {
                data.messages.forEach(message => {
                    // Only show messages for current conversation
                    if (this.currentRecipientId &&
                        (message.sender_id == this.currentRecipientId ||
                         (message.sender_id == this.currentUserId && message.recipient_id == this.currentRecipientId))) {

                        // Check if message already exists
                        if (!document.querySelector(`[data-message-id="${message.id}"]`)) {
                            this.addMessageToUI(message);
                        }
                    }

                    // Update last message ID
                    if (message.id > this.lastMessageId) {
                        this.lastMessageId = message.id;
                    }
                });

                // Refresh conversations to update unread counts
                this.loadConversations();
            }
        } catch (error) {
            console.error('Error polling for messages:', error);
        }
    }

    // Utility functions
    scrollToBottom() {
        const container = document.getElementById('chatMessages');
        container.scrollTop = container.scrollHeight;
    }

    formatTime(timestamp) {
        const date = new Date(timestamp);
        const now = new Date();
        const diff = now - date;

        if (diff < 60000) { // Less than 1 minute
            return 'Just now';
        } else if (diff < 3600000) { // Less than 1 hour
            return Math.floor(diff / 60000) + 'm ago';
        } else if (diff < 86400000) { // Less than 1 day
            return Math.floor(diff / 3600000) + 'h ago';
        } else {
            return date.toLocaleDateString();
        }
    }

    formatMessage(message, type) {
        if (type === 'image') return '📷 Image';
        if (type === 'file') return '📎 File';
        return message.length > 30 ? message.substring(0, 30) + '...' : message;
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    getFileTypeInfo(filename) {
        const extension = filename.split('.').pop().toLowerCase();

        const fileTypes = {
            // Documents
            'pdf': { icon: 'fa-file-pdf-o', type: 'pdf' },
            'doc': { icon: 'fa-file-word-o', type: 'doc' },
            'docx': { icon: 'fa-file-word-o', type: 'docx' },
            'xls': { icon: 'fa-file-excel-o', type: 'xls' },
            'xlsx': { icon: 'fa-file-excel-o', type: 'xlsx' },
            'ppt': { icon: 'fa-file-powerpoint-o', type: 'ppt' },
            'pptx': { icon: 'fa-file-powerpoint-o', type: 'pptx' },
            'txt': { icon: 'fa-file-text-o', type: 'txt' },

            // Archives
            'zip': { icon: 'fa-file-archive-o', type: 'zip' },
            'rar': { icon: 'fa-file-archive-o', type: 'rar' },
            '7z': { icon: 'fa-file-archive-o', type: 'zip' },

            // Images
            'jpg': { icon: 'fa-file-image-o', type: 'image' },
            'jpeg': { icon: 'fa-file-image-o', type: 'image' },
            'png': { icon: 'fa-file-image-o', type: 'image' },
            'gif': { icon: 'fa-file-image-o', type: 'image' },
            'bmp': { icon: 'fa-file-image-o', type: 'image' },
            'svg': { icon: 'fa-file-image-o', type: 'image' },

            // Videos
            'mp4': { icon: 'fa-file-video-o', type: 'video' },
            'avi': { icon: 'fa-file-video-o', type: 'video' },
            'mov': { icon: 'fa-file-video-o', type: 'video' },
            'wmv': { icon: 'fa-file-video-o', type: 'video' },
            'flv': { icon: 'fa-file-video-o', type: 'video' },

            // Audio
            'mp3': { icon: 'fa-file-audio-o', type: 'audio' },
            'wav': { icon: 'fa-file-audio-o', type: 'audio' },
            'flac': { icon: 'fa-file-audio-o', type: 'audio' },
            'aac': { icon: 'fa-file-audio-o', type: 'audio' }
        };

        return fileTypes[extension] || { icon: 'fa-file-o', type: 'default' };
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    destroy() {
        if (this.pollInterval) {
            clearInterval(this.pollInterval);
        }
        if (this.typingTimeout) {
            clearTimeout(this.typingTimeout);
        }
        if (this.searchTimeout) {
            clearTimeout(this.searchTimeout);
        }
    }

    // Group Chat Methods
    async loadGroups() {
        try {
            const response = await fetch('chatbox/api.php?action=get_groups');
            const data = await response.json();

            if (data.success) {
                this.renderGroups(data.groups);
            }
        } catch (error) {
            console.error('Error loading groups:', error);
        }
    }

    renderGroups(groups) {
        const container = document.getElementById('groupsList');

        if (!groups || groups.length === 0) {
            container.innerHTML = `
                <div class="text-center text-muted p-3">
                    <i class="fa fa-users-cog fa-2x mb-2"></i>
                    <p>No groups yet</p>
                    <small>Create a group to start chatting with multiple people</small>
                </div>
            `;
            return;
        }

        container.innerHTML = groups.map(group => `
            <div class="group-item" onclick="chatbox.openGroupChat(${group.id}, '${group.name}')">
                <div class="d-flex align-items-center">
                    <div class="group-avatar me-2">
                        <i class="fa fa-users"></i>
                    </div>
                    <div class="group-info flex-grow-1">
                        <div class="group-name">${group.name}</div>
                        <div class="group-members">${group.member_count} members</div>
                    </div>
                    <div class="group-actions">
                        <button class="btn btn-sm btn-outline-primary" onclick="event.stopPropagation(); chatbox.showAddMembersModal(${group.id})">
                            <i class="fa fa-user-plus"></i>
                        </button>
                    </div>
                </div>
            </div>
        `).join('');
    }

    showCreateGroupModal() {
        const modal = new bootstrap.Modal(document.getElementById('createGroupModal'));
        modal.show();
    }

    async createGroup() {
        const name = document.getElementById('groupName').value.trim();
        const description = document.getElementById('groupDescription').value.trim();

        if (!name) {
            alert('Please enter a group name');
            return;
        }

        try {
            console.log('Creating group:', { name, description });

            const response = await fetch('chatbox/api.php?action=create_group', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `name=${encodeURIComponent(name)}&description=${encodeURIComponent(description)}`
            });

            console.log('Response status:', response.status);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            console.log('Response data:', data);

            if (data.success) {
                // Close modal and refresh groups
                bootstrap.Modal.getInstance(document.getElementById('createGroupModal')).hide();
                document.getElementById('createGroupForm').reset();
                this.loadGroups();
                alert('Group created successfully!');
            } else {
                alert('Error: ' + data.error);
            }
        } catch (error) {
            console.error('Error creating group:', error);
            alert('Failed to create group: ' + error.message);
        }
    }

    showAddMembersModal(groupId) {
        this.currentGroupForMembers = groupId;
        const modal = new bootstrap.Modal(document.getElementById('addMembersModal'));
        modal.show();

        // Load available users
        this.loadUsersForGroup();
    }

    async loadUsersForGroup() {
        try {
            const response = await fetch('chatbox/api.php?action=get_users&limit=50');
            const data = await response.json();

            if (data.success) {
                this.renderUsersForGroup(data.users);
            }
        } catch (error) {
            console.error('Error loading users for group:', error);
        }
    }

    renderUsersForGroup(users) {
        const container = document.getElementById('availableUsers');

        if (!users || users.length === 0) {
            container.innerHTML = '<p class="text-muted text-center">No users found</p>';
            return;
        }

        container.innerHTML = users.map(user => `
            <div class="user-item d-flex align-items-center justify-content-between p-2 border-bottom">
                <div class="d-flex align-items-center">
                    <img src="${user.image}" alt="${user.name}" class="user-avatar me-2" style="width: 35px; height: 35px;" onerror="this.src='images/profile/thumbs/profile.png'">
                    <div>
                        <div class="user-name">${user.name}</div>
                        <div class="user-position text-muted small">${user.position || 'Employee'}</div>
                    </div>
                </div>
                <button class="btn btn-sm btn-primary" onclick="chatbox.addUserToGroup(${user.id}, '${user.name}')">
                    Add
                </button>
            </div>
        `).join('');
    }

    async addUserToGroup(userId, userName) {
        if (!this.currentGroupForMembers) {
            alert('No group selected');
            return;
        }

        try {
            const response = await fetch('chatbox/api.php?action=add_group_member', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `group_id=${this.currentGroupForMembers}&user_id=${userId}`
            });

            const data = await response.json();
            if (data.success) {
                alert(`${userName} added to group successfully!`);
                // Refresh the groups list
                this.loadGroups();
            } else {
                alert('Error: ' + data.error);
            }
        } catch (error) {
            console.error('Error adding user to group:', error);
            alert('Failed to add user to group');
        }
    }

    searchUsersForGroup(query) {
        if (this.searchTimeout) {
            clearTimeout(this.searchTimeout);
        }

        this.searchTimeout = setTimeout(async () => {
            try {
                const response = await fetch(`chatbox/api.php?action=get_users&search=${encodeURIComponent(query)}&limit=20`);
                const data = await response.json();

                if (data.success) {
                    this.renderUsersForGroup(data.users);
                }
            } catch (error) {
                console.error('Error searching users:', error);
            }
        }, 300);
    }

    async openGroupChat(groupId, groupName) {
        console.log('Opening group chat:', groupId, groupName);
        this.currentGroupId = groupId;
        this.currentChatType = 'group';
        this.currentRecipientId = null;
        this.lastMessageId = 0;

        // Show messages tab
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById('messagesTab').classList.add('active');

        // Update chat header
        document.getElementById('chatUserName').textContent = groupName;
        document.getElementById('chatUserAvatar').src = 'images/profile/thumbs/profile.png'; // Default group avatar

        // Clear messages container
        document.getElementById('messagesContainer').innerHTML = '';

        // Load group messages
        await this.loadGroupMessages();

        // Scroll to bottom
        this.scrollToBottom();
    }

    async loadGroupMessages() {
        if (!this.currentGroupId) return;

        try {
            const response = await fetch(`chatbox/api.php?action=get_group_messages&group_id=${this.currentGroupId}&last_message_id=${this.lastMessageId}`);
            const data = await response.json();

            console.log('Group messages loaded:', data);

            if (data.success) {
                const messagesContainer = document.getElementById('messagesContainer');
                data.messages.forEach(message => {
                    messagesContainer.innerHTML += this.renderMessage(message);
                    this.lastMessageId = Math.max(this.lastMessageId, message.id);
                });
                this.scrollToBottom();
            } else {
                console.error('Failed to load group messages:', data.error);
            }
        } catch (error) {
            console.error('Error loading group messages:', error);
        }
    }
}

// Initialize chatbox when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.chatbox = new ModernChatbox();

    // Set current user ID from PHP session (set in include_chatbox.php)
    if (window.currentUserId) {
        window.chatbox.currentUserId = window.currentUserId;
    }
});

// Cleanup on page unload
window.addEventListener('beforeunload', function() {
    if (window.chatbox) {
        window.chatbox.destroy();
    }
});
