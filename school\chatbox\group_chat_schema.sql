-- Group Chat Database Schema
-- Run this SQL to add group chat functionality

-- Table for group chats
CREATE TABLE IF NOT EXISTS `tbl_chat_groups` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `description` text,
  `created_by` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  PRIMARY KEY (`id`),
  KEY `idx_created_by` (`created_by`),
  FOREIGN KEY (`created_by`) REFERENCES `tbl_users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table for group members
CREATE TABLE IF NOT EXISTS `tbl_chat_group_members` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `group_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `role` enum('admin','member') NOT NULL DEFAULT 'member',
  `joined_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_group_user` (`group_id`, `user_id`),
  KEY `idx_group_id` (`group_id`),
  KEY `idx_user_id` (`user_id`),
  FOREIGN KEY (`group_id`) REFERENCES `tbl_chat_groups` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`user_id`) REFERENCES `tbl_users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Update messages table to support group messages
ALTER TABLE `tbl_chat_messages` 
ADD COLUMN `group_id` int(11) NULL AFTER `recipient_id`,
ADD COLUMN `message_type` enum('text','image','file','emoji') NOT NULL DEFAULT 'text' AFTER `message`,
ADD KEY `idx_group_id` (`group_id`),
ADD FOREIGN KEY (`group_id`) REFERENCES `tbl_chat_groups` (`id`) ON DELETE CASCADE;

-- Table for group message read status (who has read which message)
CREATE TABLE IF NOT EXISTS `tbl_chat_group_read_status` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `message_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `read_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_message_user` (`message_id`, `user_id`),
  KEY `idx_message_id` (`message_id`),
  KEY `idx_user_id` (`user_id`),
  FOREIGN KEY (`message_id`) REFERENCES `tbl_chat_messages` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`user_id`) REFERENCES `tbl_users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Update conversations table to support groups
ALTER TABLE `tbl_chat_conversations`
ADD COLUMN `group_id` int(11) NULL AFTER `id`,
ADD COLUMN `conversation_type` enum('direct','group') NOT NULL DEFAULT 'direct' AFTER `group_id`,
ADD KEY `idx_group_id` (`group_id`),
ADD FOREIGN KEY (`group_id`) REFERENCES `tbl_chat_groups` (`id`) ON DELETE CASCADE;
