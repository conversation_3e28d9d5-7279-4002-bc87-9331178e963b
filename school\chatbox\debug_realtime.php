<?php
session_start();
header('Content-Type: text/html; charset=utf-8');

echo "<h2>Real-time Chat Debug</h2>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.success { color: green; background: #e6ffe6; padding: 10px; border-radius: 4px; margin: 10px 0; }
.error { color: red; background: #ffe6e6; padding: 10px; border-radius: 4px; margin: 10px 0; }
.info { color: blue; background: #e6f3ff; padding: 10px; border-radius: 4px; margin: 10px 0; }
pre { background: #f5f5f5; padding: 10px; border-radius: 4px; overflow-x: auto; }
button { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
.test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 8px; }
</style>";

// Check if user is logged in
$user_id = null;
if (isset($_SESSION['id'])) {
    $user_id = $_SESSION['id'];
} elseif (isset($_SESSION['user_id'])) {
    $user_id = $_SESSION['user_id'];
} elseif (isset($_SESSION['userid'])) {
    $user_id = $_SESSION['userid'];
}

if (!$user_id) {
    echo "<div class='error'>❌ Please log in first to test real-time functionality</div>";
    exit;
}

echo "<div class='success'>✅ User ID: $user_id</div>";

// Test 1: Timezone Check
echo "<div class='test-section'>";
echo "<h3>1. Timezone Test</h3>";
echo "<p><strong>PHP Timezone:</strong> " . date_default_timezone_get() . "</p>";
echo "<p><strong>Current PHP Time:</strong> " . date('Y-m-d H:i:s') . "</p>";

try {
    include '../db/dbconfig.php';
    mysqli_set_charset($connection, 'utf8mb4');
    
    // Set timezone
    date_default_timezone_set('Asia/Manila');
    mysqli_query($connection, "SET time_zone = '+08:00'");
    
    echo "<p><strong>Manila PHP Time:</strong> " . date('Y-m-d H:i:s') . "</p>";
    
    $db_time = mysqli_query($connection, "SELECT NOW() as current_time");
    if ($db_time) {
        $time_row = mysqli_fetch_assoc($db_time);
        echo "<p><strong>Database Time:</strong> " . $time_row['current_time'] . "</p>";
    }
    
    echo "<div class='success'>✅ Timezone set to Manila</div>";
} catch (Exception $e) {
    echo "<div class='error'>❌ Timezone setup error: " . $e->getMessage() . "</div>";
}
echo "</div>";

// Test 2: Real-time API Test
echo "<div class='test-section'>";
echo "<h3>2. Real-time API Test</h3>";
echo "<button onclick='testRealtime()'>Test Real-time Polling</button>";
echo "<button onclick='testHeartbeat()'>Test Heartbeat</button>";
echo "<div id='realtime-result'></div>";
echo "</div>";

// Test 3: Send Message Test
echo "<div class='test-section'>";
echo "<h3>3. Send Message Test</h3>";
echo "<p>Send a test message and see if it appears in real-time:</p>";
echo "<input type='number' id='recipient-id' placeholder='Recipient User ID' value='38'>";
echo "<input type='text' id='test-message' placeholder='Test message' value='Real-time test message'>";
echo "<button onclick='sendTestMessage()'>Send Test Message</button>";
echo "<div id='send-result'></div>";
echo "</div>";

// Test 4: Message Polling Simulation
echo "<div class='test-section'>";
echo "<h3>4. Message Polling Simulation</h3>";
echo "<p>This will simulate the real-time polling that happens in the chat:</p>";
echo "<button onclick='startPollingTest()'>Start Polling Test</button>";
echo "<button onclick='stopPollingTest()'>Stop Polling Test</button>";
echo "<div id='polling-result'></div>";
echo "</div>";

// Test 5: Recent Messages Check
echo "<div class='test-section'>";
echo "<h3>5. Recent Messages Check</h3>";
if (isset($connection)) {
    $recent_query = "
        SELECT c.*, u.fname, u.lname 
        FROM tbl_chat c 
        JOIN tbl_users u ON c.sender_id = u.id 
        WHERE c.sender_id = $user_id OR c.recipient_id = $user_id 
        ORDER BY c.timestamp DESC 
        LIMIT 5
    ";
    $recent_result = mysqli_query($connection, $recent_query);
    
    if ($recent_result && mysqli_num_rows($recent_result) > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>From</th><th>To</th><th>Message</th><th>Time</th><th>Read</th></tr>";
        while ($row = mysqli_fetch_assoc($recent_result)) {
            echo "<tr>";
            echo "<td>{$row['id']}</td>";
            echo "<td>{$row['fname']} {$row['lname']} ({$row['sender_id']})</td>";
            echo "<td>{$row['recipient_id']}</td>";
            echo "<td>" . substr($row['message'], 0, 30) . "...</td>";
            echo "<td>{$row['timestamp']}</td>";
            echo "<td>" . ($row['is_read'] ? 'Yes' : 'No') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<div class='info'>No recent messages found</div>";
    }
}
echo "</div>";

?>

<script>
let pollingInterval = null;

async function testRealtime() {
    const resultDiv = document.getElementById('realtime-result');
    resultDiv.innerHTML = '<div class="info">Testing real-time API...</div>';
    
    try {
        const lastCheck = new Date(Date.now() - 60000).toISOString(); // 1 minute ago
        const response = await fetch(`chat_realtime.php?action=poll&last_check=${encodeURIComponent(lastCheck)}`);
        const text = await response.text();
        
        let resultHTML = '<h4>Real-time API Response:</h4>';
        resultHTML += '<p><strong>Status:</strong> ' + response.status + '</p>';
        resultHTML += '<p><strong>Raw Response:</strong></p>';
        resultHTML += '<pre>' + text + '</pre>';
        
        try {
            const json = JSON.parse(text);
            resultHTML += '<div class="success">✅ Valid JSON Response</div>';
            resultHTML += '<p><strong>Parsed Data:</strong></p>';
            resultHTML += '<pre>' + JSON.stringify(json, null, 2) + '</pre>';
        } catch (e) {
            resultHTML += '<div class="error">❌ Invalid JSON: ' + e.message + '</div>';
        }
        
        resultDiv.innerHTML = resultHTML;
        
    } catch (error) {
        resultDiv.innerHTML = '<div class="error">❌ Error: ' + error.message + '</div>';
    }
}

async function testHeartbeat() {
    const resultDiv = document.getElementById('realtime-result');
    resultDiv.innerHTML = '<div class="info">Testing heartbeat...</div>';
    
    try {
        const response = await fetch('chat_realtime.php?action=heartbeat');
        const text = await response.text();
        
        resultDiv.innerHTML = '<h4>Heartbeat Response:</h4><pre>' + text + '</pre>';
        
    } catch (error) {
        resultDiv.innerHTML = '<div class="error">❌ Heartbeat Error: ' + error.message + '</div>';
    }
}

async function sendTestMessage() {
    const resultDiv = document.getElementById('send-result');
    const recipientId = document.getElementById('recipient-id').value;
    const message = document.getElementById('test-message').value;
    
    if (!recipientId || !message) {
        resultDiv.innerHTML = '<div class="error">❌ Please enter recipient ID and message</div>';
        return;
    }
    
    resultDiv.innerHTML = '<div class="info">Sending test message...</div>';
    
    try {
        const response = await fetch('chat_api.php?action=send_message', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                recipient_id: recipientId,
                message: message
            })
        });
        
        const text = await response.text();
        
        let resultHTML = '<h4>Send Message Response:</h4>';
        resultHTML += '<pre>' + text + '</pre>';
        
        try {
            const json = JSON.parse(text);
            if (json.success) {
                resultHTML += '<div class="success">✅ Message sent successfully! ID: ' + json.message_id + '</div>';
                resultHTML += '<p>Now check if it appears in real-time polling...</p>';
            } else {
                resultHTML += '<div class="error">❌ Send failed: ' + (json.error || 'Unknown error') + '</div>';
            }
        } catch (e) {
            resultHTML += '<div class="error">❌ Invalid JSON response</div>';
        }
        
        resultDiv.innerHTML = resultHTML;
        
    } catch (error) {
        resultDiv.innerHTML = '<div class="error">❌ Send Error: ' + error.message + '</div>';
    }
}

function startPollingTest() {
    const resultDiv = document.getElementById('polling-result');
    resultDiv.innerHTML = '<div class="info">Starting polling test... (will poll every 3 seconds)</div>';
    
    let pollCount = 0;
    pollingInterval = setInterval(async () => {
        pollCount++;
        
        try {
            const lastCheck = new Date(Date.now() - 10000).toISOString(); // 10 seconds ago
            const response = await fetch(`chat_realtime.php?action=poll&last_check=${encodeURIComponent(lastCheck)}`);
            const data = await response.json();
            
            let status = `<p><strong>Poll #${pollCount}:</strong> `;
            if (data.success) {
                status += `✅ Success - ${data.new_messages.length} new messages, ${data.status_updates.length} status updates</p>`;
                
                if (data.new_messages.length > 0) {
                    status += '<div class="success">🎉 NEW MESSAGES DETECTED!</div>';
                    status += '<pre>' + JSON.stringify(data.new_messages, null, 2) + '</pre>';
                }
            } else {
                status += `❌ Failed - ${data.error || 'Unknown error'}</p>`;
            }
            
            resultDiv.innerHTML = status + resultDiv.innerHTML;
            
        } catch (error) {
            resultDiv.innerHTML = `<p><strong>Poll #${pollCount}:</strong> ❌ Error - ${error.message}</p>` + resultDiv.innerHTML;
        }
    }, 3000);
}

function stopPollingTest() {
    if (pollingInterval) {
        clearInterval(pollingInterval);
        pollingInterval = null;
        document.getElementById('polling-result').innerHTML = '<div class="info">Polling test stopped</div>' + document.getElementById('polling-result').innerHTML;
    }
}
</script>

<div class="info">
<h3>How to Test Real-time Functionality:</h3>
<ol>
<li><strong>Test Real-time API:</strong> Click "Test Real-time Polling" to see if the API responds</li>
<li><strong>Send Test Message:</strong> Send a message to yourself or another user</li>
<li><strong>Start Polling Test:</strong> This simulates what the chat does automatically</li>
<li><strong>Check for Issues:</strong> Look for any errors in the responses</li>
</ol>

<h3>Common Issues:</h3>
<ul>
<li><strong>Timezone Mismatch:</strong> Database and PHP should both use Manila time</li>
<li><strong>Polling Errors:</strong> Real-time API should return valid JSON</li>
<li><strong>Message Not Appearing:</strong> Check if polling detects new messages</li>
<li><strong>Network Issues:</strong> Online hosting might have different network behavior</li>
</ul>
</div>
