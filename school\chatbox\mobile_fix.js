/**
 * Mobile Chat Input Fix
 * Ensures chat input is visible and properly positioned on mobile devices
 */

(function() {
    'use strict';
    
    // Check if we're on mobile
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    
    if (!isMobile) return; // Only run on mobile devices
    
    let initialViewportHeight = window.innerHeight;
    let isKeyboardOpen = false;
    
    // Add mobile-specific CSS
    function addMobileCSS() {
        const style = document.createElement('style');
        style.textContent = `
            /* Mobile Chat Input Fixes */
            @media (max-width: 768px) {
                /* Ensure viewport units work correctly */
                :root {
                    --vh: ${window.innerHeight * 0.01}px;
                }
                
                /* Fix chat input positioning */
                .chat-input, #message-input-area {
                    position: fixed !important;
                    bottom: 0 !important;
                    left: 0 !important;
                    right: 0 !important;
                    z-index: 9999 !important;
                    background: white !important;
                    border-top: 1px solid #e9ecef !important;
                    box-shadow: 0 -2px 10px rgba(0,0,0,0.1) !important;
                    padding: 10px !important;
                    padding-bottom: max(env(safe-area-inset-bottom), 10px) !important;
                }
                
                /* Adjust chat messages container */
                .chat-messages {
                    padding-bottom: 80px !important;
                    margin-bottom: 0 !important;
                }
                
                /* Fix chat window height */
                .chat-panel, #chat-window {
                    height: calc(var(--vh, 1vh) * 100) !important;
                    max-height: calc(var(--vh, 1vh) * 100) !important;
                }
                
                /* Input styling */
                #message-input {
                    font-size: 16px !important; /* Prevents zoom on iOS */
                    min-height: 44px !important;
                    border-radius: 22px !important;
                    padding: 12px 16px !important;
                    resize: none !important;
                    max-height: 120px !important;
                    overflow-y: auto !important;
                }
                
                /* Button styling */
                .chat-input .btn {
                    min-width: 44px !important;
                    min-height: 44px !important;
                    border-radius: 50% !important;
                    padding: 10px !important;
                }
                
                /* When keyboard is open */
                .keyboard-open {
                    position: fixed !important;
                    bottom: 0 !important;
                    transform: translateY(0) !important;
                }
                
                /* Adjust for keyboard */
                .chat-messages.keyboard-open {
                    height: calc(var(--vh, 1vh) * 50) !important;
                }
            }
        `;
        document.head.appendChild(style);
    }
    
    // Handle viewport height changes (keyboard open/close)
    function handleViewportChange() {
        const currentHeight = window.innerHeight;
        const heightDifference = initialViewportHeight - currentHeight;
        
        // Update CSS custom property
        document.documentElement.style.setProperty('--vh', `${currentHeight * 0.01}px`);
        
        const chatInput = document.querySelector('.chat-input, #message-input-area');
        const chatMessages = document.querySelector('.chat-messages');
        
        if (heightDifference > 150) { // Keyboard is likely open
            isKeyboardOpen = true;
            if (chatInput) {
                chatInput.classList.add('keyboard-open');
            }
            if (chatMessages) {
                chatMessages.classList.add('keyboard-open');
            }
        } else {
            isKeyboardOpen = false;
            if (chatInput) {
                chatInput.classList.remove('keyboard-open');
            }
            if (chatMessages) {
                chatMessages.classList.remove('keyboard-open');
            }
        }
    }
    
    // Handle input focus
    function handleInputFocus() {
        const messageInput = document.getElementById('message-input');
        if (!messageInput) return;
        
        messageInput.addEventListener('focus', function() {
            // Delay to allow keyboard to open
            setTimeout(() => {
                // Scroll input into view
                this.scrollIntoView({ 
                    behavior: 'smooth', 
                    block: 'center',
                    inline: 'nearest'
                });
                
                // Scroll chat to bottom
                if (window.chatbox && window.chatbox.scrollToBottom) {
                    window.chatbox.scrollToBottom();
                }
            }, 300);
        });
        
        messageInput.addEventListener('blur', function() {
            // Small delay to allow keyboard to close
            setTimeout(() => {
                const chatInput = document.querySelector('.chat-input, #message-input-area');
                if (chatInput) {
                    chatInput.classList.remove('keyboard-open');
                }
            }, 100);
        });
        
        // Auto-resize textarea
        messageInput.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = Math.min(this.scrollHeight, 120) + 'px';
        });
    }
    
    // Force scroll to bottom when chat opens
    function handleChatOpen() {
        const chatToggle = document.getElementById('chat-toggle');
        if (chatToggle) {
            chatToggle.addEventListener('click', function() {
                setTimeout(() => {
                    if (window.chatbox && window.chatbox.scrollToBottom) {
                        window.chatbox.scrollToBottom();
                    }
                }, 500);
            });
        }
    }
    
    // Initialize mobile fixes
    function init() {
        addMobileCSS();
        
        // Set up event listeners
        window.addEventListener('resize', handleViewportChange);
        window.addEventListener('orientationchange', function() {
            setTimeout(() => {
                initialViewportHeight = window.innerHeight;
                handleViewportChange();
            }, 500);
        });
        
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', function() {
                handleInputFocus();
                handleChatOpen();
            });
        } else {
            handleInputFocus();
            handleChatOpen();
        }
        
        // Re-check when chatbox is loaded
        const checkChatbox = setInterval(() => {
            if (window.chatbox) {
                handleInputFocus();
                handleChatOpen();
                clearInterval(checkChatbox);
            }
        }, 1000);
    }
    
    // Start initialization
    init();
    
    // Expose utility functions
    window.mobileChatFix = {
        forceInputVisible: function() {
            const messageInput = document.getElementById('message-input');
            if (messageInput) {
                messageInput.scrollIntoView({ 
                    behavior: 'smooth', 
                    block: 'center' 
                });
            }
        },
        
        adjustForKeyboard: function() {
            handleViewportChange();
        },
        
        isKeyboardOpen: function() {
            return isKeyboardOpen;
        }
    };
    
})();
