<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Mobile Chat Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #f8f9fa;
            font-family: Arial, sans-serif;
        }
        
        .test-content {
            height: 200vh;
            background: linear-gradient(to bottom, #e3f2fd, #bbdefb);
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .mobile-info {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 8px;
            font-size: 12px;
            z-index: 10000;
        }
        
        .test-buttons {
            position: fixed;
            top: 10px;
            left: 10px;
            z-index: 10000;
        }
        
        .test-buttons button {
            margin: 2px;
            font-size: 12px;
            padding: 5px 10px;
        }
    </style>
</head>
<body>
    <div class="mobile-info" id="mobile-info">
        <div>Screen: <span id="screen-size"></span></div>
        <div>Viewport: <span id="viewport-size"></span></div>
        <div>Keyboard: <span id="keyboard-status">Closed</span></div>
        <div>Device: <span id="device-type"></span></div>
    </div>
    
    <div class="test-buttons">
        <button class="btn btn-sm btn-primary" onclick="testScrollToInput()">Scroll to Input</button>
        <button class="btn btn-sm btn-success" onclick="testOpenChat()">Open Chat</button>
        <button class="btn btn-sm btn-warning" onclick="testForceVisible()">Force Visible</button>
    </div>
    
    <div class="test-content">
        <h1>Mobile Chat Input Test</h1>
        <p>This page tests the mobile chat input visibility fixes.</p>
        
        <h3>Instructions:</h3>
        <ol>
            <li>Open this page on your mobile device</li>
            <li>Click the chat button (bottom right)</li>
            <li>Try to type a message</li>
            <li>The input should be visible above the keyboard</li>
        </ol>
        
        <h3>Test Scenarios:</h3>
        <ul>
            <li>Portrait mode</li>
            <li>Landscape mode</li>
            <li>Keyboard open/close</li>
            <li>Scrolling while typing</li>
        </ul>
        
        <div style="height: 100px; background: #fff; margin: 20px 0; padding: 20px; border-radius: 8px;">
            <h4>Sample Content</h4>
            <p>This is sample content to test scrolling behavior.</p>
        </div>
        
        <div style="height: 100px; background: #fff; margin: 20px 0; padding: 20px; border-radius: 8px;">
            <h4>More Content</h4>
            <p>More content to ensure the page is scrollable.</p>
        </div>
    </div>
    
    <!-- Include the chatbox -->
    <?php include 'chatbox.php'; ?>
    
    <!-- Include scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="mobile_fix.js"></script>
    <script src="chatbox.js"></script>
    
    <script>
        // Update mobile info display
        function updateMobileInfo() {
            document.getElementById('screen-size').textContent = `${screen.width}x${screen.height}`;
            document.getElementById('viewport-size').textContent = `${window.innerWidth}x${window.innerHeight}`;
            
            const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
            document.getElementById('device-type').textContent = isMobile ? 'Mobile' : 'Desktop';
            
            // Check if keyboard is likely open
            const heightDiff = screen.height - window.innerHeight;
            document.getElementById('keyboard-status').textContent = heightDiff > 150 ? 'Open' : 'Closed';
        }
        
        // Test functions
        function testScrollToInput() {
            const messageInput = document.getElementById('message-input');
            if (messageInput) {
                messageInput.scrollIntoView({ behavior: 'smooth', block: 'center' });
                messageInput.focus();
            } else {
                alert('Chat input not found. Open chat first.');
            }
        }
        
        function testOpenChat() {
            const chatToggle = document.getElementById('chat-toggle');
            if (chatToggle) {
                chatToggle.click();
                setTimeout(() => {
                    const messageInput = document.getElementById('message-input');
                    if (messageInput) {
                        messageInput.focus();
                    }
                }, 500);
            }
        }
        
        function testForceVisible() {
            if (window.mobileChatFix) {
                window.mobileChatFix.forceInputVisible();
            } else {
                alert('Mobile fix not loaded');
            }
        }
        
        // Update info on load and resize
        updateMobileInfo();
        window.addEventListener('resize', updateMobileInfo);
        window.addEventListener('orientationchange', () => {
            setTimeout(updateMobileInfo, 500);
        });
        
        // Log mobile events
        console.log('Mobile test page loaded');
        console.log('User agent:', navigator.userAgent);
        console.log('Screen size:', screen.width + 'x' + screen.height);
        console.log('Viewport size:', window.innerWidth + 'x' + window.innerHeight);
    </script>
</body>
</html>
