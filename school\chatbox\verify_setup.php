<?php
session_start();
header('Content-Type: text/html; charset=utf-8');
include '../db/dbconfig.php';

// Set UTF-8 encoding for database connection
mysqli_set_charset($connection, 'utf8mb4');

echo "<h2>Chat System Setup Verification</h2>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
.container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
.success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 4px; margin: 10px 0; }
.error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 4px; margin: 10px 0; }
.warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 4px; margin: 10px 0; }
.info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 4px; margin: 10px 0; }
table { border-collapse: collapse; width: 100%; margin: 10px 0; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f8f9fa; }
.step { margin: 20px 0; padding: 15px; border-left: 4px solid #007bff; background: #f8f9fa; }
</style>";

echo "<div class='container'>";

// Step 1: Check database connection
echo "<div class='step'>";
echo "<h3>Step 1: Database Connection</h3>";
if ($connection) {
    echo "<div class='success'>✅ Database connected successfully</div>";
    echo "<div class='info'>Server: " . mysqli_get_server_info($connection) . "</div>";
    echo "<div class='info'>Charset: " . mysqli_character_set_name($connection) . "</div>";
} else {
    echo "<div class='error'>❌ Database connection failed: " . mysqli_connect_error() . "</div>";
    echo "</div></div>";
    exit;
}
echo "</div>";

// Step 2: Check if chat tables exist
echo "<div class='step'>";
echo "<h3>Step 2: Chat Tables Check</h3>";
$required_tables = [
    'tbl_chat' => 'Main chat messages table',
    'tbl_users' => 'Users table (should already exist)',
    'tbl_chat_files' => 'File attachments table',
    'tbl_conversations' => 'Conversations optimization table',
    'tbl_user_status' => 'User online status table',
    'tbl_chat_notifications' => 'Notifications table'
];

$missing_tables = [];
foreach ($required_tables as $table => $description) {
    $result = mysqli_query($connection, "SHOW TABLES LIKE '$table'");
    if (mysqli_num_rows($result) > 0) {
        echo "<div class='success'>✅ $table - $description</div>";
    } else {
        echo "<div class='error'>❌ $table - $description (MISSING)</div>";
        $missing_tables[] = $table;
    }
}

if (!empty($missing_tables)) {
    echo "<div class='warning'>";
    echo "<strong>Missing Tables Found!</strong><br>";
    echo "Please run the SQL script: <code>create_chat_tables.sql</code><br>";
    echo "Missing: " . implode(', ', $missing_tables);
    echo "</div>";
} else {
    echo "<div class='success'>✅ All required tables exist!</div>";
}
echo "</div>";

// Step 3: Check session
echo "<div class='step'>";
echo "<h3>Step 3: Session Check</h3>";
if (empty($_SESSION)) {
    echo "<div class='warning'>⚠️ No session data found. Please log in first.</div>";
} else {
    echo "<div class='info'>Session ID: " . session_id() . "</div>";
    
    // Try to find user ID
    $user_id = null;
    $session_var = null;
    if (isset($_SESSION['id'])) {
        $user_id = $_SESSION['id'];
        $session_var = 'id';
    } elseif (isset($_SESSION['user_id'])) {
        $user_id = $_SESSION['user_id'];
        $session_var = 'user_id';
    } elseif (isset($_SESSION['userid'])) {
        $user_id = $_SESSION['userid'];
        $session_var = 'userid';
    }
    
    if ($user_id) {
        echo "<div class='success'>✅ User ID found: $user_id (from \$_SESSION['$session_var'])</div>";
        
        // Check if user exists in database
        $user_check = mysqli_query($connection, "SELECT fname, lname FROM tbl_users WHERE id = $user_id");
        if ($user_check && mysqli_num_rows($user_check) > 0) {
            $user_data = mysqli_fetch_assoc($user_check);
            echo "<div class='success'>✅ User exists in database: {$user_data['fname']} {$user_data['lname']}</div>";
        } else {
            echo "<div class='error'>❌ User ID $user_id not found in tbl_users table</div>";
        }
    } else {
        echo "<div class='error'>❌ No user ID found in session</div>";
        echo "<div class='info'>Available session variables: " . implode(', ', array_keys($_SESSION)) . "</div>";
    }
}
echo "</div>";

// Step 4: Test chat functionality (only if tables exist and user is logged in)
if (empty($missing_tables) && isset($user_id)) {
    echo "<div class='step'>";
    echo "<h3>Step 4: Chat Functionality Test</h3>";
    
    // Check for existing messages
    $message_count = mysqli_query($connection, "SELECT COUNT(*) as count FROM tbl_chat WHERE sender_id = $user_id OR recipient_id = $user_id");
    $count_data = mysqli_fetch_assoc($message_count);
    
    if ($count_data['count'] > 0) {
        echo "<div class='success'>✅ Found {$count_data['count']} messages involving this user</div>";
        
        // Show recent conversations
        $conversations = mysqli_query($connection, "
            SELECT DISTINCT 
                CASE WHEN sender_id = $user_id THEN recipient_id ELSE sender_id END as other_user_id,
                COUNT(*) as msg_count,
                MAX(timestamp) as last_msg
            FROM tbl_chat 
            WHERE sender_id = $user_id OR recipient_id = $user_id
            GROUP BY other_user_id
            ORDER BY last_msg DESC
            LIMIT 5
        ");
        
        if (mysqli_num_rows($conversations) > 0) {
            echo "<h4>Your Recent Conversations:</h4>";
            echo "<table>";
            echo "<tr><th>User</th><th>Messages</th><th>Last Message</th></tr>";
            while ($conv = mysqli_fetch_assoc($conversations)) {
                $other_user = mysqli_query($connection, "SELECT fname, lname FROM tbl_users WHERE id = {$conv['other_user_id']}");
                $other_data = mysqli_fetch_assoc($other_user);
                echo "<tr>";
                echo "<td>{$other_data['fname']} {$other_data['lname']} (ID: {$conv['other_user_id']})</td>";
                echo "<td>{$conv['msg_count']}</td>";
                echo "<td>{$conv['last_msg']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    } else {
        echo "<div class='info'>ℹ️ No messages found. Start a conversation to test the chat system.</div>";
    }
    
    // Test API endpoint
    echo "<h4>API Test:</h4>";
    echo "<button onclick='testAPI()' style='padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;'>Test Chat API</button>";
    echo "<div id='api-result' style='margin-top: 10px;'></div>";
    
    echo "</div>";
}

// Step 5: Next steps
echo "<div class='step'>";
echo "<h3>Step 5: Next Steps</h3>";
if (!empty($missing_tables)) {
    echo "<div class='warning'>";
    echo "<strong>Action Required:</strong><br>";
    echo "1. Run the SQL script: <code>create_chat_tables.sql</code> in phpMyAdmin<br>";
    echo "2. Refresh this page to verify tables were created<br>";
    echo "3. Test the chat functionality";
    echo "</div>";
} elseif (!isset($user_id)) {
    echo "<div class='warning'>";
    echo "<strong>Action Required:</strong><br>";
    echo "1. Log in to your account<br>";
    echo "2. Refresh this page to verify session<br>";
    echo "3. Test the chat functionality";
    echo "</div>";
} else {
    echo "<div class='success'>";
    echo "<strong>Setup Complete!</strong><br>";
    echo "✅ Database tables exist<br>";
    echo "✅ User session is active<br>";
    echo "✅ Chat system should be working<br><br>";
    echo "You can now:<br>";
    echo "• Go to any page with the chatbox<br>";
    echo "• Click the chat button<br>";
    echo "• Start conversations with other users";
    echo "</div>";
}
echo "</div>";

echo "</div>";

// JavaScript for API testing
echo "<script>
async function testAPI() {
    const resultDiv = document.getElementById('api-result');
    resultDiv.innerHTML = '<div style=\"color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 4px;\">Testing API...</div>';
    
    try {
        const response = await fetch('chat_api.php?action=get_conversations');
        const data = await response.json();
        
        if (data.error) {
            resultDiv.innerHTML = '<div style=\"color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 4px;\">❌ API Error: ' + data.error + '</div>';
        } else {
            resultDiv.innerHTML = '<div style=\"color: #28a745; background: #d4edda; padding: 10px; border-radius: 4px;\">✅ API working! Response: <pre>' + JSON.stringify(data, null, 2) + '</pre></div>';
        }
    } catch (error) {
        resultDiv.innerHTML = '<div style=\"color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 4px;\">❌ Network Error: ' + error.message + '</div>';
    }
}
</script>";
?>
