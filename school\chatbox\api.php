<?php
session_start();
header('Content-Type: application/json; charset=utf-8');
header('Cache-Control: no-cache, must-revalidate');
header('Expires: 0');

include '../db/dbconfig.php';

// Set timezone and encoding
date_default_timezone_set('Asia/Manila');
mysqli_set_charset($connection, 'utf8mb4');
mysqli_query($connection, "SET time_zone = '+08:00'");

// Get current user ID
$current_user_id = $_SESSION['id'] ?? $_SESSION['user_id'] ?? $_SESSION['userid'] ?? null;

if (!$current_user_id) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit;
}

$action = $_GET['action'] ?? $_POST['action'] ?? '';

try {
    switch ($action) {
        case 'get_conversations':
            getConversations($connection, $current_user_id);
            break;
        case 'get_messages':
            getMessages($connection, $current_user_id);
            break;
        case 'send_message':
            sendMessage($connection, $current_user_id);
            break;
        case 'upload_file':
            uploadFile($connection, $current_user_id);
            break;
        case 'mark_read':
            markAsRead($connection, $current_user_id);
            break;
        case 'get_users':
            getUsers($connection, $current_user_id);
            break;
        case 'update_status':
            updateOnlineStatus($connection, $current_user_id);
            break;
        case 'get_new_messages':
            getNewMessages($connection, $current_user_id);
            break;
        case 'set_typing':
            setTypingStatus($connection, $current_user_id);
            break;
        default:
            http_response_code(400);
            echo json_encode(['success' => false, 'error' => 'Invalid action']);
    }
} catch (Exception $e) {
    error_log("Chat API Error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Internal server error']);
}

function getConversations($connection, $user_id) {
    $stmt = $connection->prepare("
        SELECT 
            c.*,
            u.fname, u.lname, u.image,
            m.message, m.message_type, m.created_at as last_message_time,
            os.status as online_status,
            CASE 
                WHEN c.user1_id = ? THEN c.user2_unread 
                ELSE c.user1_unread 
            END as unread_count,
            CASE 
                WHEN c.user1_id = ? THEN c.user2_id 
                ELSE c.user1_id 
            END as other_user_id
        FROM tbl_chat_conversations c
        LEFT JOIN tbl_chat_messages m ON c.last_message_id = m.id
        LEFT JOIN tbl_users u ON (
            CASE 
                WHEN c.user1_id = ? THEN c.user2_id = u.id
                ELSE c.user1_id = u.id
            END
        )
        LEFT JOIN tbl_chat_online_status os ON u.id = os.user_id
        WHERE c.user1_id = ? OR c.user2_id = ?
        ORDER BY c.last_activity DESC
    ");
    
    $stmt->bind_param("iiiii", $user_id, $user_id, $user_id, $user_id, $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $conversations = [];
    while ($row = $result->fetch_assoc()) {
        $conversations[] = [
            'id' => $row['id'],
            'user_id' => $row['other_user_id'],
            'user_name' => $row['fname'] . ' ' . $row['lname'],
            'user_image' => $row['image'] ? $row['path'] : 'images/profile/thumbs/profile.png',
            'last_message' => $row['message'] ?: 'No messages yet',
            'last_message_type' => $row['message_type'] ?: 'text',
            'last_message_time' => $row['last_message_time'],
            'unread_count' => (int)$row['unread_count'],
            'online_status' => $row['online_status'] ?: 'offline'
        ];
    }
    
    echo json_encode(['success' => true, 'conversations' => $conversations]);
}

function getMessages($connection, $user_id) {
    $recipient_id = $_GET['recipient_id'] ?? 0;
    $limit = $_GET['limit'] ?? 50;
    $offset = $_GET['offset'] ?? 0;
    
    if (!$recipient_id) {
        echo json_encode(['success' => false, 'error' => 'Recipient ID required']);
        return;
    }
    
    $stmt = $connection->prepare("
        SELECT 
            m.*,
            u.fname, u.lname, u.image
        FROM tbl_chat_messages m
        JOIN tbl_users u ON m.sender_id = u.id
        WHERE ((m.sender_id = ? AND m.recipient_id = ?) OR (m.sender_id = ? AND m.recipient_id = ?))
          AND m.is_deleted = 0
        ORDER BY m.created_at DESC
        LIMIT ? OFFSET ?
    ");
    
    $stmt->bind_param("iiiiii", $user_id, $recipient_id, $recipient_id, $user_id, $limit, $offset);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $messages = [];
    while ($row = $result->fetch_assoc()) {
        $messages[] = [
            'id' => $row['id'],
            'sender_id' => $row['sender_id'],
            'recipient_id' => $row['recipient_id'],
            'message' => $row['message'],
            'message_type' => $row['message_type'],
            'file_path' => $row['file_path'],
            'file_name' => $row['file_name'],
            'file_size' => $row['file_size'],
            'file_type' => $row['file_type'],
            'is_read' => (bool)$row['is_read'],
            'created_at' => $row['created_at'],
            'sender_name' => $row['fname'] . ' ' . $row['lname'],
            'sender_image' => $row['image'] ? $row['path'] : 'images/profile/thumbs/profile.png'
        ];
    }
    
    // Reverse to show oldest first
    $messages = array_reverse($messages);

    echo json_encode(['success' => true, 'messages' => $messages]);
}

function sendMessage($connection, $user_id) {
    $input = json_decode(file_get_contents('php://input'), true);
    $recipient_id = $input['recipient_id'] ?? 0;
    $message = $input['message'] ?? '';
    $message_type = $input['message_type'] ?? 'text';

    if (!$recipient_id || (!$message && $message_type === 'text')) {
        echo json_encode(['success' => false, 'error' => 'Invalid input']);
        return;
    }

    // Insert message
    $stmt = $connection->prepare("
        INSERT INTO tbl_chat_messages (sender_id, recipient_id, message, message_type, created_at)
        VALUES (?, ?, ?, ?, NOW())
    ");
    $stmt->bind_param("iiss", $user_id, $recipient_id, $message, $message_type);

    if ($stmt->execute()) {
        $message_id = $connection->insert_id;

        // Update or create conversation
        updateConversation($connection, $user_id, $recipient_id, $message_id);

        echo json_encode([
            'success' => true,
            'message_id' => $message_id,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    } else {
        echo json_encode(['success' => false, 'error' => 'Failed to send message']);
    }
}

function uploadFile($connection, $user_id) {
    $recipient_id = $_POST['recipient_id'] ?? 0;

    if (!$recipient_id || !isset($_FILES['file'])) {
        echo json_encode(['success' => false, 'error' => 'Invalid input']);
        return;
    }

    $file = $_FILES['file'];
    $upload_dir = 'uploads/chat/';

    // Create directory if it doesn't exist
    if (!is_dir($upload_dir)) {
        mkdir($upload_dir, 0755, true);
    }

    // Generate unique filename
    $file_extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $unique_name = uniqid() . '_' . time() . '.' . $file_extension;
    $file_path = $upload_dir . $unique_name;

    if (move_uploaded_file($file['tmp_name'], $file_path)) {
        // Determine message type
        $message_type = 'file';
        $mime_type = mime_content_type($file_path);
        if (strpos($mime_type, 'image/') === 0) {
            $message_type = 'image';
        }

        // Insert message
        $stmt = $connection->prepare("
            INSERT INTO tbl_chat_messages
            (sender_id, recipient_id, message, message_type, file_path, file_name, file_size, file_type, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
        ");

        $message = $message_type === 'image' ? '📷 Image' : '📎 ' . $file['name'];
        $stmt->bind_param("iissssss",
            $user_id, $recipient_id, $message, $message_type,
            $file_path, $file['name'], $file['size'], $mime_type
        );

        if ($stmt->execute()) {
            $message_id = $connection->insert_id;
            updateConversation($connection, $user_id, $recipient_id, $message_id);

            echo json_encode([
                'success' => true,
                'message_id' => $message_id,
                'file_path' => $file_path,
                'file_name' => $file['name'],
                'message_type' => $message_type
            ]);
        } else {
            unlink($file_path); // Delete uploaded file if database insert fails
            echo json_encode(['success' => false, 'error' => 'Failed to save file info']);
        }
    } else {
        echo json_encode(['success' => false, 'error' => 'Failed to upload file']);
    }
}

function updateConversation($connection, $user1_id, $user2_id, $message_id) {
    // Ensure user1_id is always smaller for consistency
    if ($user1_id > $user2_id) {
        $temp = $user1_id;
        $user1_id = $user2_id;
        $user2_id = $temp;
    }

    // Check if conversation exists
    $stmt = $connection->prepare("
        SELECT id, user1_unread, user2_unread FROM tbl_chat_conversations
        WHERE user1_id = ? AND user2_id = ?
    ");
    $stmt->bind_param("ii", $user1_id, $user2_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($row = $result->fetch_assoc()) {
        // Update existing conversation
        $new_user1_unread = $row['user1_unread'];
        $new_user2_unread = $row['user2_unread'];

        // Increment unread count for recipient
        if ($_SESSION['id'] == $user1_id) {
            $new_user2_unread++;
        } else {
            $new_user1_unread++;
        }

        $stmt = $connection->prepare("
            UPDATE tbl_chat_conversations
            SET last_message_id = ?, last_activity = NOW(), user1_unread = ?, user2_unread = ?
            WHERE user1_id = ? AND user2_id = ?
        ");
        $stmt->bind_param("iiiii", $message_id, $new_user1_unread, $new_user2_unread, $user1_id, $user2_id);
        $stmt->execute();
    } else {
        // Create new conversation
        $user1_unread = ($_SESSION['id'] == $user1_id) ? 0 : 1;
        $user2_unread = ($_SESSION['id'] == $user2_id) ? 0 : 1;

        $stmt = $connection->prepare("
            INSERT INTO tbl_chat_conversations (user1_id, user2_id, last_message_id, user1_unread, user2_unread)
            VALUES (?, ?, ?, ?, ?)
        ");
        $stmt->bind_param("iiiii", $user1_id, $user2_id, $message_id, $user1_unread, $user2_unread);
        $stmt->execute();
    }
}

function markAsRead($connection, $user_id) {
    $sender_id = $_POST['sender_id'] ?? 0;

    if (!$sender_id) {
        echo json_encode(['success' => false, 'error' => 'Sender ID required']);
        return;
    }

    // Mark messages as read
    $stmt = $connection->prepare("
        UPDATE tbl_chat_messages
        SET is_read = 1
        WHERE sender_id = ? AND recipient_id = ? AND is_read = 0
    ");
    $stmt->bind_param("ii", $sender_id, $user_id);
    $stmt->execute();

    // Update conversation unread count
    $user1_id = min($user_id, $sender_id);
    $user2_id = max($user_id, $sender_id);

    $unread_field = ($user_id == $user1_id) ? 'user1_unread' : 'user2_unread';

    $stmt = $connection->prepare("
        UPDATE tbl_chat_conversations
        SET $unread_field = 0
        WHERE user1_id = ? AND user2_id = ?
    ");
    $stmt->bind_param("ii", $user1_id, $user2_id);
    $stmt->execute();

    echo json_encode(['success' => true]);
}

function getUsers($connection, $user_id) {
    $search = $_GET['search'] ?? '';
    $limit = $_GET['limit'] ?? 20;

    $where_clause = "WHERE u.id != ?";
    $params = [$user_id];
    $types = "i";

    if ($search) {
        $where_clause .= " AND (u.fname LIKE ? OR u.lname LIKE ? OR CONCAT(u.fname, ' ', u.lname) LIKE ?)";
        $search_param = "%$search%";
        $params = array_merge($params, [$search_param, $search_param, $search_param]);
        $types .= "sss";
    }

    $stmt = $connection->prepare("
        SELECT
            u.id, u.fname, u.lname, u.image, u.position,
            os.status as online_status, os.last_seen
        FROM tbl_users u
        LEFT JOIN tbl_chat_online_status os ON u.id = os.user_id
        $where_clause
        ORDER BY
            CASE WHEN os.status = 'online' THEN 1 ELSE 2 END,
            u.fname, u.lname
        LIMIT ?
    ");

    $params[] = $limit;
    $types .= "i";

    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();

    $users = [];
    while ($row = $result->fetch_assoc()) {
        $users[] = [
            'id' => $row['id'],
            'name' => $row['fname'] . ' ' . $row['lname'],
            'image' => $row['image'] ? $row['path'] : 'images/profile/thumbs/profile.png',
            'position' => $row['position'],
            'online_status' => $row['online_status'] ?: 'offline',
            'last_seen' => $row['last_seen']
        ];
    }

    echo json_encode(['success' => true, 'users' => $users]);
}

function updateOnlineStatus($connection, $user_id) {
    $status = $_POST['status'] ?? 'online';

    $stmt = $connection->prepare("
        INSERT INTO tbl_chat_online_status (user_id, status, last_seen, last_activity)
        VALUES (?, ?, NOW(), NOW())
        ON DUPLICATE KEY UPDATE
        status = VALUES(status),
        last_seen = NOW(),
        last_activity = NOW()
    ");
    $stmt->bind_param("is", $user_id, $status);
    $stmt->execute();

    echo json_encode(['success' => true]);
}

function getNewMessages($connection, $user_id) {
    $last_message_id = $_GET['last_message_id'] ?? 0;

    $stmt = $connection->prepare("
        SELECT
            m.*,
            u.fname, u.lname, u.image
        FROM tbl_chat_messages m
        JOIN tbl_users u ON m.sender_id = u.id
        WHERE (m.recipient_id = ? OR m.sender_id = ?)
          AND m.id > ?
          AND m.is_deleted = 0
        ORDER BY m.created_at ASC
        LIMIT 50
    ");

    $stmt->bind_param("iii", $user_id, $user_id, $last_message_id);
    $stmt->execute();
    $result = $stmt->get_result();

    $messages = [];
    while ($row = $result->fetch_assoc()) {
        $messages[] = [
            'id' => $row['id'],
            'sender_id' => $row['sender_id'],
            'recipient_id' => $row['recipient_id'],
            'message' => $row['message'],
            'message_type' => $row['message_type'],
            'file_path' => $row['file_path'],
            'file_name' => $row['file_name'],
            'file_size' => $row['file_size'],
            'file_type' => $row['file_type'],
            'is_read' => (bool)$row['is_read'],
            'created_at' => $row['created_at'],
            'sender_name' => $row['fname'] . ' ' . $row['lname'],
            'sender_image' => $row['image'] ? $row['path'] : 'images/profile/thumbs/profile.png'
        ];
    }

    echo json_encode(['success' => true, 'messages' => $messages]);
}

function setTypingStatus($connection, $user_id) {
    $recipient_id = $_POST['recipient_id'] ?? 0;
    $is_typing = $_POST['is_typing'] ?? 0;

    if (!$recipient_id) {
        echo json_encode(['success' => false, 'error' => 'Recipient ID required']);
        return;
    }

    $stmt = $connection->prepare("
        INSERT INTO tbl_chat_typing (user_id, recipient_id, is_typing, updated_at)
        VALUES (?, ?, ?, NOW())
        ON DUPLICATE KEY UPDATE
        is_typing = VALUES(is_typing),
        updated_at = NOW()
    ");
    $stmt->bind_param("iii", $user_id, $recipient_id, $is_typing);
    $stmt->execute();

    echo json_encode(['success' => true]);
}
?>
