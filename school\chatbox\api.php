<?php
session_start();
header('Content-Type: application/json; charset=utf-8');
header('Cache-Control: no-cache, must-revalidate');
header('Expires: 0');

include '../db/dbconfig.php';

// Helper function to get valid image path
function getValidImagePath($filename) {
    if (empty($filename)) {
        return 'images/profile/thumbs/profile.png';
    }

    // Return the path directly - let the browser handle missing images with onerror
    return 'images/profile/thumbs/' . $filename;
}

// Set timezone and encoding
date_default_timezone_set('Asia/Manila');
mysqli_set_charset($connection, 'utf8mb4');
mysqli_query($connection, "SET time_zone = '+08:00'");

// Get current user ID
$current_user_id = $_SESSION['id'] ?? $_SESSION['user_id'] ?? $_SESSION['userid'] ?? null;

if (!$current_user_id) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit;
}

$action = $_GET['action'] ?? $_POST['action'] ?? '';

try {
    switch ($action) {
        case 'get_conversations':
            getConversations($connection, $current_user_id);
            break;
        case 'get_messages':
            getMessages($connection, $current_user_id);
            break;
        case 'send_message':
            sendMessage($connection, $current_user_id);
            break;
        case 'upload_file':
            uploadFile($connection, $current_user_id);
            break;
        case 'mark_read':
            markAsRead($connection, $current_user_id);
            break;
        case 'get_users':
            getUsers($connection, $current_user_id);
            break;
        case 'update_status':
            updateOnlineStatus($connection, $current_user_id);
            break;
        case 'get_new_messages':
            getNewMessages($connection, $current_user_id);
            break;
        case 'set_typing':
            setTypingStatus($connection, $current_user_id);
            break;
        case 'get_current_user':
            getCurrentUser($connection, $current_user_id);
            break;
        case 'create_group':
            createGroup($connection, $current_user_id);
            break;
        case 'get_groups':
            getGroups($connection, $current_user_id);
            break;
        case 'add_group_member':
            addGroupMember($connection, $current_user_id);
            break;
        case 'get_group_members':
            getGroupMembers($connection, $current_user_id);
            break;
        case 'send_group_message':
            sendGroupMessage($connection, $current_user_id);
            break;
        case 'get_group_messages':
            getGroupMessages($connection, $current_user_id);
            break;
        default:
            http_response_code(400);
            echo json_encode(['success' => false, 'error' => 'Invalid action']);
    }
} catch (Exception $e) {
    error_log("Chat API Error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Internal server error']);
}

function getConversations($connection, $user_id) {
    $stmt = $connection->prepare("
        SELECT
            c.*,
            u.fname, u.lname, u.image,
            u.image as user_image_filename,
            m.message, m.message_type, m.created_at as last_message_time,
            os.status as online_status,
            CASE
                WHEN c.user1_id = ? THEN c.user1_unread
                ELSE c.user2_unread
            END as unread_count,
            CASE 
                WHEN c.user1_id = ? THEN c.user2_id 
                ELSE c.user1_id 
            END as other_user_id
        FROM tbl_chat_conversations c
        LEFT JOIN tbl_chat_messages m ON c.last_message_id = m.id
        LEFT JOIN tbl_users u ON (
            CASE 
                WHEN c.user1_id = ? THEN c.user2_id = u.id
                ELSE c.user1_id = u.id
            END
        )
        LEFT JOIN tbl_chat_online_status os ON u.id = os.user_id
        WHERE c.user1_id = ? OR c.user2_id = ?
        ORDER BY c.last_activity DESC
    ");
    
    $stmt->bind_param("iiiii", $user_id, $user_id, $user_id, $user_id, $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $conversations = [];
    while ($row = $result->fetch_assoc()) {
        $conversations[] = [
            'id' => $row['id'],
            'user_id' => $row['other_user_id'],
            'user_name' => $row['fname'] . ' ' . $row['lname'],
            'user_image' => getValidImagePath($row['user_image_filename']),
            'last_message' => $row['message'] ?: 'No messages yet',
            'last_message_type' => $row['message_type'] ?: 'text',
            'last_message_time' => $row['last_message_time'],
            'unread_count' => (int)$row['unread_count'],
            'online_status' => $row['online_status'] ?: 'offline'
        ];
    }
    
    echo json_encode(['success' => true, 'conversations' => $conversations]);
}

function getMessages($connection, $user_id) {
    $recipient_id = $_GET['recipient_id'] ?? 0;
    $limit = $_GET['limit'] ?? 50;
    $offset = $_GET['offset'] ?? 0;
    
    if (!$recipient_id) {
        echo json_encode(['success' => false, 'error' => 'Recipient ID required']);
        return;
    }
    
    $stmt = $connection->prepare("
        SELECT
            m.*,
            u.fname, u.lname, u.image
        FROM tbl_chat_messages m
        JOIN tbl_users u ON m.sender_id = u.id
        WHERE ((m.sender_id = ? AND m.recipient_id = ?) OR (m.sender_id = ? AND m.recipient_id = ?))
          AND m.is_deleted = 0
        ORDER BY m.created_at DESC
        LIMIT ? OFFSET ?
    ");
    
    $stmt->bind_param("iiiiii", $user_id, $recipient_id, $recipient_id, $user_id, $limit, $offset);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $messages = [];
    while ($row = $result->fetch_assoc()) {
        $messages[] = [
            'id' => $row['id'],
            'sender_id' => $row['sender_id'],
            'recipient_id' => $row['recipient_id'],
            'message' => $row['message'],
            'message_type' => $row['message_type'],
            'file_path' => $row['file_path'],
            'file_name' => $row['file_name'],
            'file_size' => $row['file_size'],
            'file_type' => $row['file_type'],
            'is_read' => (bool)$row['is_read'],
            'created_at' => $row['created_at'],
            'sender_name' => $row['fname'] . ' ' . $row['lname'],
            'sender_image' => getValidImagePath($row['image'])
        ];
    }
    
    // Reverse to show oldest first
    $messages = array_reverse($messages);

    echo json_encode(['success' => true, 'messages' => $messages]);
}

function sendMessage($connection, $user_id) {
    $input = json_decode(file_get_contents('php://input'), true);
    $recipient_id = $input['recipient_id'] ?? 0;
    $message = $input['message'] ?? '';
    $message_type = $input['message_type'] ?? 'text';

    if (!$recipient_id || (!$message && $message_type === 'text')) {
        echo json_encode(['success' => false, 'error' => 'Invalid input']);
        return;
    }

    // Insert message
    $stmt = $connection->prepare("
        INSERT INTO tbl_chat_messages (sender_id, recipient_id, message, message_type, created_at)
        VALUES (?, ?, ?, ?, NOW())
    ");
    $stmt->bind_param("iiss", $user_id, $recipient_id, $message, $message_type);

    if ($stmt->execute()) {
        $message_id = $connection->insert_id;

        // Update or create conversation
        updateConversation($connection, $user_id, $recipient_id, $message_id);

        echo json_encode([
            'success' => true,
            'message_id' => $message_id,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    } else {
        echo json_encode(['success' => false, 'error' => 'Failed to send message']);
    }
}

function uploadFile($connection, $user_id) {
    $recipient_id = $_POST['recipient_id'] ?? 0;

    if (!$recipient_id || !isset($_FILES['file'])) {
        echo json_encode(['success' => false, 'error' => 'Invalid input']);
        return;
    }

    $file = $_FILES['file'];
    $upload_dir = 'uploads/chat/';

    // Create directory if it doesn't exist
    if (!is_dir($upload_dir)) {
        mkdir($upload_dir, 0755, true);
    }

    // Generate unique filename
    $file_extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $unique_name = uniqid() . '_' . time() . '.' . $file_extension;
    $file_path = $upload_dir . $unique_name;
    $relative_path = 'chatbox/' . $file_path; // Path relative to school directory

    if (move_uploaded_file($file['tmp_name'], $file_path)) {
        // Determine message type
        $message_type = 'file';
        $mime_type = mime_content_type($file_path);
        if (strpos($mime_type, 'image/') === 0) {
            $message_type = 'image';
        }

        // Insert message
        $stmt = $connection->prepare("
            INSERT INTO tbl_chat_messages
            (sender_id, recipient_id, message, message_type, file_path, file_name, file_size, file_type, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
        ");

        $message = $message_type === 'image' ? '📷 Image' : '📎 ' . $file['name'];
        $stmt->bind_param("iissssss",
            $user_id, $recipient_id, $message, $message_type,
            $relative_path, $file['name'], $file['size'], $mime_type
        );

        if ($stmt->execute()) {
            $message_id = $connection->insert_id;
            updateConversation($connection, $user_id, $recipient_id, $message_id);

            echo json_encode([
                'success' => true,
                'message_id' => $message_id,
                'file_path' => $relative_path,
                'file_name' => $file['name'],
                'message_type' => $message_type
            ]);
        } else {
            unlink($file_path); // Delete uploaded file if database insert fails
            echo json_encode(['success' => false, 'error' => 'Failed to save file info']);
        }
    } else {
        echo json_encode(['success' => false, 'error' => 'Failed to upload file']);
    }
}

function updateConversation($connection, $user1_id, $user2_id, $message_id) {
    // Ensure user1_id is always smaller for consistency
    if ($user1_id > $user2_id) {
        $temp = $user1_id;
        $user1_id = $user2_id;
        $user2_id = $temp;
    }

    // Check if conversation exists
    $stmt = $connection->prepare("
        SELECT id, user1_unread, user2_unread FROM tbl_chat_conversations
        WHERE user1_id = ? AND user2_id = ?
    ");
    $stmt->bind_param("ii", $user1_id, $user2_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($row = $result->fetch_assoc()) {
        // Update existing conversation
        $new_user1_unread = $row['user1_unread'];
        $new_user2_unread = $row['user2_unread'];

        // Increment unread count for recipient
        if ($_SESSION['id'] == $user1_id) {
            $new_user2_unread++;
        } else {
            $new_user1_unread++;
        }

        $stmt = $connection->prepare("
            UPDATE tbl_chat_conversations
            SET last_message_id = ?, last_activity = NOW(), user1_unread = ?, user2_unread = ?
            WHERE user1_id = ? AND user2_id = ?
        ");
        $stmt->bind_param("iiiii", $message_id, $new_user1_unread, $new_user2_unread, $user1_id, $user2_id);
        $stmt->execute();
    } else {
        // Create new conversation
        $user1_unread = ($_SESSION['id'] == $user1_id) ? 0 : 1;
        $user2_unread = ($_SESSION['id'] == $user2_id) ? 0 : 1;

        $stmt = $connection->prepare("
            INSERT INTO tbl_chat_conversations (user1_id, user2_id, last_message_id, user1_unread, user2_unread)
            VALUES (?, ?, ?, ?, ?)
        ");
        $stmt->bind_param("iiiii", $user1_id, $user2_id, $message_id, $user1_unread, $user2_unread);
        $stmt->execute();
    }
}

function markAsRead($connection, $user_id) {
    $sender_id = $_POST['sender_id'] ?? 0;

    if (!$sender_id) {
        echo json_encode(['success' => false, 'error' => 'Sender ID required']);
        return;
    }

    // Mark messages as read
    $stmt = $connection->prepare("
        UPDATE tbl_chat_messages
        SET is_read = 1
        WHERE sender_id = ? AND recipient_id = ? AND is_read = 0
    ");
    $stmt->bind_param("ii", $sender_id, $user_id);
    $stmt->execute();

    $messages_updated = $stmt->affected_rows;

    // Update conversation unread count
    $user1_id = min($user_id, $sender_id);
    $user2_id = max($user_id, $sender_id);

    if ($user_id == $user1_id) {
        $stmt = $connection->prepare("
            UPDATE tbl_chat_conversations
            SET user1_unread = 0
            WHERE user1_id = ? AND user2_id = ?
        ");
    } else {
        $stmt = $connection->prepare("
            UPDATE tbl_chat_conversations
            SET user2_unread = 0
            WHERE user1_id = ? AND user2_id = ?
        ");
    }
    $stmt->bind_param("ii", $user1_id, $user2_id);
    $stmt->execute();

    $conversations_updated = $stmt->affected_rows;

    echo json_encode([
        'success' => true,
        'messages_updated' => $messages_updated,
        'conversations_updated' => $conversations_updated,
        'debug' => [
            'sender_id' => $sender_id,
            'recipient_id' => $user_id,
            'user1_id' => $user1_id,
            'user2_id' => $user2_id
        ]
    ]);
}

function getUsers($connection, $user_id) {
    $search = $_GET['search'] ?? '';
    $limit = $_GET['limit'] ?? 20;

    $where_clause = "WHERE u.id != ?";
    $params = [$user_id];
    $types = "i";

    if ($search) {
        $where_clause .= " AND (u.fname LIKE ? OR u.lname LIKE ? OR CONCAT(u.fname, ' ', u.lname) LIKE ?)";
        $search_param = "%$search%";
        $params = array_merge($params, [$search_param, $search_param, $search_param]);
        $types .= "sss";
    }

    $stmt = $connection->prepare("
        SELECT
            u.id, u.fname, u.lname, u.image, u.position,
            os.status as online_status, os.last_seen
        FROM tbl_users u
        LEFT JOIN tbl_chat_online_status os ON u.id = os.user_id
        $where_clause
        ORDER BY
            CASE WHEN os.status = 'online' THEN 1 ELSE 2 END,
            u.fname, u.lname
        LIMIT ?
    ");

    $params[] = $limit;
    $types .= "i";

    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();

    $users = [];
    while ($row = $result->fetch_assoc()) {
        $users[] = [
            'id' => $row['id'],
            'name' => $row['fname'] . ' ' . $row['lname'],
            'image' => getValidImagePath($row['image']),
            'position' => $row['position'],
            'online_status' => $row['online_status'] ?: 'offline',
            'last_seen' => $row['last_seen']
        ];
    }

    echo json_encode(['success' => true, 'users' => $users]);
}

function updateOnlineStatus($connection, $user_id) {
    $status = $_POST['status'] ?? 'online';

    $stmt = $connection->prepare("
        INSERT INTO tbl_chat_online_status (user_id, status, last_seen, last_activity)
        VALUES (?, ?, NOW(), NOW())
        ON DUPLICATE KEY UPDATE
        status = VALUES(status),
        last_seen = NOW(),
        last_activity = NOW()
    ");
    $stmt->bind_param("is", $user_id, $status);
    $stmt->execute();

    echo json_encode(['success' => true]);
}

function getNewMessages($connection, $user_id) {
    $last_message_id = $_GET['last_message_id'] ?? 0;

    $stmt = $connection->prepare("
        SELECT
            m.*,
            u.fname, u.lname, u.image
        FROM tbl_chat_messages m
        JOIN tbl_users u ON m.sender_id = u.id
        WHERE (m.recipient_id = ? OR m.sender_id = ?)
          AND m.id > ?
          AND m.is_deleted = 0
        ORDER BY m.created_at ASC
        LIMIT 50
    ");

    $stmt->bind_param("iii", $user_id, $user_id, $last_message_id);
    $stmt->execute();
    $result = $stmt->get_result();

    $messages = [];
    while ($row = $result->fetch_assoc()) {
        $messages[] = [
            'id' => $row['id'],
            'sender_id' => $row['sender_id'],
            'recipient_id' => $row['recipient_id'],
            'message' => $row['message'],
            'message_type' => $row['message_type'],
            'file_path' => $row['file_path'],
            'file_name' => $row['file_name'],
            'file_size' => $row['file_size'],
            'file_type' => $row['file_type'],
            'is_read' => (bool)$row['is_read'],
            'created_at' => $row['created_at'],
            'sender_name' => $row['fname'] . ' ' . $row['lname'],
            'sender_image' => getValidImagePath($row['image'])
        ];
    }

    echo json_encode(['success' => true, 'messages' => $messages]);
}

function setTypingStatus($connection, $user_id) {
    $recipient_id = $_POST['recipient_id'] ?? 0;
    $is_typing = $_POST['is_typing'] ?? 0;

    if (!$recipient_id) {
        echo json_encode(['success' => false, 'error' => 'Recipient ID required']);
        return;
    }

    $stmt = $connection->prepare("
        INSERT INTO tbl_chat_typing (user_id, recipient_id, is_typing, updated_at)
        VALUES (?, ?, ?, NOW())
        ON DUPLICATE KEY UPDATE
        is_typing = VALUES(is_typing),
        updated_at = NOW()
    ");
    $stmt->bind_param("iii", $user_id, $recipient_id, $is_typing);
    $stmt->execute();

    echo json_encode(['success' => true]);
}

function getCurrentUser($connection, $user_id) {
    $stmt = $connection->prepare("
        SELECT id, fname, lname, image
        FROM tbl_users
        WHERE id = ?
    ");

    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($row = $result->fetch_assoc()) {
        echo json_encode([
            'success' => true,
            'user_id' => $row['id'],
            'name' => $row['fname'] . ' ' . $row['lname'],
            'image' => getValidImagePath($row['image'])
        ]);
    } else {
        echo json_encode(['success' => false, 'error' => 'User not found']);
    }
}

// Group Chat Functions
function createGroup($connection, $user_id) {
    try {
        $name = $_POST['name'] ?? '';
        $description = $_POST['description'] ?? '';

        if (empty($name)) {
            echo json_encode(['success' => false, 'error' => 'Group name is required']);
            return;
        }

        // Check if tables exist first
        $check_table = $connection->query("SHOW TABLES LIKE 'tbl_chat_groups'");
        if ($check_table->num_rows == 0) {
            echo json_encode(['success' => false, 'error' => 'Group tables not found. Please run the database schema first.']);
            return;
        }

        // Create group
        $stmt = $connection->prepare("
            INSERT INTO tbl_chat_groups (name, description, created_by)
            VALUES (?, ?, ?)
        ");

        if (!$stmt) {
            echo json_encode(['success' => false, 'error' => 'Database prepare error: ' . $connection->error]);
            return;
        }

        $stmt->bind_param("ssi", $name, $description, $user_id);

        if ($stmt->execute()) {
            $group_id = $connection->insert_id;

            // Add creator as admin member
            $stmt = $connection->prepare("
                INSERT INTO tbl_chat_group_members (group_id, user_id, role)
                VALUES (?, ?, 'admin')
            ");

            if ($stmt) {
                $stmt->bind_param("ii", $group_id, $user_id);
                $stmt->execute();
            }

            echo json_encode([
                'success' => true,
                'group_id' => $group_id,
                'message' => 'Group created successfully'
            ]);
        } else {
            echo json_encode(['success' => false, 'error' => 'Failed to create group: ' . $stmt->error]);
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => 'Exception: ' . $e->getMessage()]);
    }
}

function getGroups($connection, $user_id) {
    $stmt = $connection->prepare("
        SELECT g.*,
               u.fname as creator_fname, u.lname as creator_lname,
               COUNT(gm.user_id) as member_count
        FROM tbl_chat_groups g
        LEFT JOIN tbl_users u ON g.created_by = u.id
        LEFT JOIN tbl_chat_group_members gm ON g.id = gm.group_id AND gm.is_active = 1
        WHERE g.id IN (
            SELECT group_id FROM tbl_chat_group_members
            WHERE user_id = ? AND is_active = 1
        ) AND g.is_active = 1
        GROUP BY g.id
        ORDER BY g.updated_at DESC
    ");

    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();

    $groups = [];
    while ($row = $result->fetch_assoc()) {
        $groups[] = [
            'id' => $row['id'],
            'name' => $row['name'],
            'description' => $row['description'],
            'created_by' => $row['created_by'],
            'creator_name' => $row['creator_fname'] . ' ' . $row['creator_lname'],
            'member_count' => (int)$row['member_count'],
            'created_at' => $row['created_at']
        ];
    }

    echo json_encode(['success' => true, 'groups' => $groups]);
}

function addGroupMember($connection, $user_id) {
    $group_id = $_POST['group_id'] ?? 0;
    $member_user_id = $_POST['user_id'] ?? 0;

    if (!$group_id || !$member_user_id) {
        echo json_encode(['success' => false, 'error' => 'Group ID and User ID are required']);
        return;
    }

    // Check if current user is admin of the group
    $stmt = $connection->prepare("
        SELECT role FROM tbl_chat_group_members
        WHERE group_id = ? AND user_id = ? AND is_active = 1
    ");
    $stmt->bind_param("ii", $group_id, $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $row = $result->fetch_assoc();

    if (!$row || $row['role'] !== 'admin') {
        echo json_encode(['success' => false, 'error' => 'Only group admins can add members']);
        return;
    }

    // Add member to group
    $stmt = $connection->prepare("
        INSERT INTO tbl_chat_group_members (group_id, user_id, role)
        VALUES (?, ?, 'member')
        ON DUPLICATE KEY UPDATE is_active = 1
    ");
    $stmt->bind_param("ii", $group_id, $member_user_id);

    if ($stmt->execute()) {
        echo json_encode(['success' => true, 'message' => 'Member added successfully']);
    } else {
        echo json_encode(['success' => false, 'error' => 'Failed to add member']);
    }
}

function getGroupMembers($connection, $user_id) {
    $group_id = $_GET['group_id'] ?? 0;

    if (!$group_id) {
        echo json_encode(['success' => false, 'error' => 'Group ID is required']);
        return;
    }

    // Check if user is member of the group
    $stmt = $connection->prepare("
        SELECT 1 FROM tbl_chat_group_members
        WHERE group_id = ? AND user_id = ? AND is_active = 1
    ");
    $stmt->bind_param("ii", $group_id, $user_id);
    $stmt->execute();

    if (!$stmt->get_result()->fetch_assoc()) {
        echo json_encode(['success' => false, 'error' => 'Access denied']);
        return;
    }

    // Get group members
    $stmt = $connection->prepare("
        SELECT u.id, u.fname, u.lname, u.image, gm.role, gm.joined_at
        FROM tbl_chat_group_members gm
        JOIN tbl_users u ON gm.user_id = u.id
        WHERE gm.group_id = ? AND gm.is_active = 1
        ORDER BY gm.role DESC, gm.joined_at ASC
    ");
    $stmt->bind_param("i", $group_id);
    $stmt->execute();
    $result = $stmt->get_result();

    $members = [];
    while ($row = $result->fetch_assoc()) {
        $members[] = [
            'id' => $row['id'],
            'name' => $row['fname'] . ' ' . $row['lname'],
            'image' => getValidImagePath($row['image']),
            'role' => $row['role'],
            'joined_at' => $row['joined_at']
        ];
    }

    echo json_encode(['success' => true, 'members' => $members]);
}

function sendGroupMessage($connection, $user_id) {
    try {
        $group_id = $_POST['group_id'] ?? 0;
        $message = $_POST['message'] ?? '';
        $message_type = $_POST['message_type'] ?? 'text';

        if (!$group_id || empty($message)) {
            echo json_encode(['success' => false, 'error' => 'Group ID and message are required']);
            return;
        }

        // Check if user is member of the group
        $stmt = $connection->prepare("
            SELECT 1 FROM tbl_chat_group_members
            WHERE group_id = ? AND user_id = ? AND is_active = 1
        ");

        if (!$stmt) {
            echo json_encode(['success' => false, 'error' => 'Database prepare error: ' . $connection->error]);
            return;
        }

        $stmt->bind_param("ii", $group_id, $user_id);
        $stmt->execute();

        if (!$stmt->get_result()->fetch_assoc()) {
            echo json_encode(['success' => false, 'error' => 'Access denied - not a group member']);
            return;
        }

        // Insert group message
        $stmt = $connection->prepare("
            INSERT INTO tbl_chat_messages (sender_id, group_id, message, message_type, created_at)
            VALUES (?, ?, ?, ?, NOW())
        ");

        if (!$stmt) {
            echo json_encode(['success' => false, 'error' => 'Database prepare error: ' . $connection->error]);
            return;
        }

        $stmt->bind_param("iiss", $user_id, $group_id, $message, $message_type);

        if ($stmt->execute()) {
            $message_id = $connection->insert_id;

            echo json_encode([
                'success' => true,
                'message_id' => $message_id,
                'timestamp' => date('Y-m-d H:i:s')
            ]);
        } else {
            echo json_encode(['success' => false, 'error' => 'Failed to send message: ' . $stmt->error]);
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => 'Exception: ' . $e->getMessage()]);
    }
}

function getGroupMessages($connection, $user_id) {
    $group_id = $_GET['group_id'] ?? 0;
    $last_message_id = $_GET['last_message_id'] ?? 0;

    if (!$group_id) {
        echo json_encode(['success' => false, 'error' => 'Group ID is required']);
        return;
    }

    // Check if user is member of the group
    $stmt = $connection->prepare("
        SELECT 1 FROM tbl_chat_group_members
        WHERE group_id = ? AND user_id = ? AND is_active = 1
    ");
    $stmt->bind_param("ii", $group_id, $user_id);
    $stmt->execute();

    if (!$stmt->get_result()->fetch_assoc()) {
        echo json_encode(['success' => false, 'error' => 'Access denied']);
        return;
    }

    // Get group messages
    $stmt = $connection->prepare("
        SELECT m.*, u.fname, u.lname, u.image
        FROM tbl_chat_messages m
        JOIN tbl_users u ON m.sender_id = u.id
        WHERE m.group_id = ? AND m.id > ?
        ORDER BY m.created_at ASC
        LIMIT 50
    ");
    $stmt->bind_param("ii", $group_id, $last_message_id);
    $stmt->execute();
    $result = $stmt->get_result();

    $messages = [];
    while ($row = $result->fetch_assoc()) {
        $messages[] = [
            'id' => $row['id'],
            'sender_id' => $row['sender_id'],
            'sender_name' => $row['fname'] . ' ' . $row['lname'],
            'sender_image' => getValidImagePath($row['image']),
            'message' => $row['message'],
            'message_type' => $row['message_type'],
            'created_at' => $row['created_at'],
            'group_id' => $row['group_id']
        ];
    }

    echo json_encode(['success' => true, 'messages' => $messages]);
}
?>