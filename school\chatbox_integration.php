<?php
// Chatbox Integration Script
// Include this file in all pages within the /school/ directory

// Only show chatbox if user is logged in
if (isset($_SESSION['id'])) {
?>
    <!-- Chatbox Integration -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    
    <!-- Include Modern Chatbox -->
    <?php include 'chatbox/include_chatbox.php'; ?>
    
    <script>
        // Additional chatbox initialization (the main initialization is in include_chatbox.php)
        document.addEventListener('DOMContentLoaded', function() {
            // Ensure Bootstrap modals work properly with chatbox
            const modals = document.querySelectorAll('[data-bs-toggle="modal"]');
            modals.forEach(function(modal) {
                modal.addEventListener('click', function(e) {
                    // Ensure modal clicks work properly
                    e.stopPropagation();
                });
            });

            // Allow Bootstrap dropdowns to work normally
            // Removed stopPropagation that was interfering with dropdown behavior
        });
    </script>
    
    <style>
        /* Mobile viewport fix */
        @media (max-width: 768px) {
            /* Ensure full screen on mobile */
            .chat-panel, .chat-window {
                position: fixed !important;
                top: 0 !important;
                left: 0 !important;
                right: 0 !important;
                bottom: 0 !important;
                width: 100vw !important;
                height: 100vh !important;
                max-width: 100vw !important;
                max-height: 100vh !important;
                border-radius: 0 !important;
                transform: translateX(100%) !important;
                transition: transform 0.3s ease !important;
            }

            .chat-panel.open, .chat-window.open {
                transform: translateX(0) !important;
            }

            .chat-toggle {
                bottom: 50px !important;
                right: 20px !important;
                z-index: 998 !important;
            }

            /* Prevent body scroll when chat is open */
            body.chat-open {
                overflow: hidden;
                position: fixed;
                width: 100%;
            }

            /* Mobile safe area adjustments for newer phones */
            @supports (padding: max(0px)) {
                .chat-panel, .chat-window {
                    padding-top: max(env(safe-area-inset-top), 0px);
                    padding-bottom: max(env(safe-area-inset-bottom), 0px);
                }

                .chat-header, .chat-window-header {
                    padding-top: max(env(safe-area-inset-top), 10px);
                }
            }
        }
        
        /* Ensure chatbox appears above other elements but below Bootstrap modals */
        #chatContainer {
            z-index: 999 !important;
        }

        #chatToggle {
            z-index: 998 !important;
        }

        /* Ensure Bootstrap modals work properly */
        .modal {
            z-index: 1055 !important;
        }

        .modal-backdrop {
            z-index: 1050 !important;
        }
        
        /* Custom scrollbar for better UX */
        .chat-messages::-webkit-scrollbar {
            width: 8px;
        }
        
        .chat-messages::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }
        
        .chat-messages::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
        }
        
        .chat-messages::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }
        
        /* Animation for new messages */
        @keyframes slideInMessage {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .message {
            animation: slideInMessage 0.3s ease-out;
        }
        
        /* Pulse animation for notification badge */
        @keyframes pulse {
            0% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.1);
            }
            100% {
                transform: scale(1);
            }
        }
        
        .chat-badge {
            animation: pulse 2s infinite;
        }
        
        /* Typing indicator animation */
        @keyframes typing {
            0%, 60%, 100% {
                transform: translateY(0);
            }
            30% {
                transform: translateY(-10px);
            }
        }
        
        .typing-indicator i {
            animation: typing 1.4s infinite;
        }
        
        /* Status indicator glow effect */
        .status-online {
            box-shadow: 0 0 6px #28a745;
        }
        
        .status-away {
            box-shadow: 0 0 6px #ffc107;
        }
        
        .status-busy {
            box-shadow: 0 0 6px #dc3545;
        }
        
        /* Hover effects */
        .chat-list-item:hover {
            background-color: #f8f9fa;
            transform: translateX(2px);
            transition: all 0.2s ease;
        }
        
        .chat-toggle:hover {
            transform: translateY(-50%) scale(1.05);
            box-shadow: -3px 0 15px rgba(0,0,0,0.2);
        }
        
        /* Message bubble improvements */
        .message-content {
            word-wrap: break-word;
            max-width: 280px;
        }
        
        .message.own .message-content {
            background: linear-gradient(135deg, #007bff, #0056b3);
        }
        
        /* File message styling */
        .message-file {
            transition: background-color 0.2s ease;
        }
        
        .message-file:hover {
            background-color: #e9ecef;
        }
        
        /* Loading states */
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }
        
        .spinner-border-sm {
            width: 1rem;
            height: 1rem;
        }
    </style>
<?php
}
?>
