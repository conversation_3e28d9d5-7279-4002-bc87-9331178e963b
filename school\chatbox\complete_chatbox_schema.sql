-- =====================================================
-- COMPLETE CHATBOX SYSTEM DATABASE SCHEMA
-- =====================================================
-- This file contains all necessary database changes for the complete chatbox system
-- Features: Real-time chat, file sharing, emoji support, message editing/deletion,
-- notifications, user status, conversation management, and performance optimizations
-- =====================================================

-- Set proper charset for the session
SET NAMES utf8mb4;
SET CHARACTER SET utf8mb4;

-- =====================================================
-- MAIN CHAT TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS `tbl_chat` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `sender_id` int(11) NOT NULL,
  `recipient_id` int(11) NOT NULL,
  `message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `message_type` enum('text','image','file') DEFAULT 'text',
  `timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `is_read` tinyint(1) DEFAULT '0',
  `read_at` timestamp NULL DEFAULT NULL,
  `is_deleted` tinyint(1) DEFAULT '0',
  `is_edited` tinyint(1) DEFAULT '0',
  `edited_at` timestamp NULL DEFAULT NULL,
  `original_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `deleted_for` enum('none', 'sender', 'both') DEFAULT 'none',
  PRIMARY KEY (`id`),
  KEY `idx_sender_recipient` (`sender_id`,`recipient_id`),
  KEY `idx_recipient_timestamp` (`recipient_id`, `timestamp`),
  KEY `idx_sender_timestamp` (`sender_id`, `timestamp`),
  KEY `idx_timestamp` (`timestamp`),
  KEY `idx_is_read` (`is_read`),
  KEY `idx_conversation` (`sender_id`, `recipient_id`, `timestamp`),
  KEY `idx_unread_messages` (`recipient_id`, `is_read`, `timestamp`),
  KEY `idx_edited_deleted` (`is_edited`, `is_deleted`, `deleted_for`),
  KEY `idx_message_type` (`message_type`),
  KEY `idx_deleted_for` (`deleted_for`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- CHAT FILES TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS `tbl_chat_files` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `message_id` int(11) NOT NULL,
  `original_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `file_path` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `file_size` int(11) DEFAULT NULL,
  `file_type` varchar(100) DEFAULT NULL,
  `mime_type` varchar(100) DEFAULT NULL,
  `upload_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `is_deleted` tinyint(1) DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `idx_message_id` (`message_id`),
  KEY `idx_file_type` (`file_type`),
  KEY `idx_upload_time` (`upload_time`),
  FOREIGN KEY (`message_id`) REFERENCES `tbl_chat` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- CONVERSATIONS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS `tbl_conversations` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user1_id` int(11) NOT NULL,
  `user2_id` int(11) NOT NULL,
  `last_message_id` int(11) DEFAULT NULL,
  `last_message_preview` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `last_activity` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `user1_unread_count` int(11) DEFAULT '0',
  `user2_unread_count` int(11) DEFAULT '0',
  `user1_deleted` tinyint(1) DEFAULT '0',
  `user2_deleted` tinyint(1) DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_conversation` (`user1_id`,`user2_id`),
  KEY `idx_last_activity` (`last_activity`),
  KEY `idx_user1_unread` (`user1_id`,`user1_unread_count`),
  KEY `idx_user2_unread` (`user2_id`,`user2_unread_count`),
  KEY `idx_user1_active` (`user1_id`, `user1_deleted`, `last_activity`),
  KEY `idx_user2_active` (`user2_id`, `user2_deleted`, `last_activity`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- USER STATUS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS `tbl_user_status` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `status` enum('online','away','busy','offline') DEFAULT 'offline',
  `custom_status` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `last_seen` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_activity` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_typing_to` int(11) DEFAULT NULL,
  `typing_started_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_user_status` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_last_seen` (`last_seen`),
  KEY `idx_last_activity` (`last_activity`),
  KEY `idx_typing` (`is_typing_to`, `typing_started_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- CHAT NOTIFICATIONS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS `tbl_chat_notifications` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `message_id` int(11) NOT NULL,
  `sender_id` int(11) NOT NULL,
  `notification_type` enum('message','mention','system','file','image') DEFAULT 'message',
  `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_read` tinyint(1) DEFAULT '0',
  `read_at` timestamp NULL DEFAULT NULL,
  `is_clicked` tinyint(1) DEFAULT '0',
  `clicked_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `expires_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_user_unread` (`user_id`,`is_read`),
  KEY `idx_user_recent` (`user_id`, `created_at`),
  KEY `idx_message_id` (`message_id`),
  KEY `idx_sender_id` (`sender_id`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_notification_type` (`notification_type`),
  KEY `idx_expires_at` (`expires_at`),
  FOREIGN KEY (`message_id`) REFERENCES `tbl_chat` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- CHAT SETTINGS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS `tbl_chat_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `notifications_enabled` tinyint(1) DEFAULT '1',
  `sound_enabled` tinyint(1) DEFAULT '1',
  `desktop_notifications` tinyint(1) DEFAULT '1',
  `email_notifications` tinyint(1) DEFAULT '0',
  `show_online_status` tinyint(1) DEFAULT '1',
  `show_read_receipts` tinyint(1) DEFAULT '1',
  `show_typing_indicator` tinyint(1) DEFAULT '1',
  `auto_delete_messages` int(11) DEFAULT NULL,
  `message_preview_length` int(11) DEFAULT '50',
  `theme` varchar(50) DEFAULT 'default',
  `font_size` enum('small','medium','large') DEFAULT 'medium',
  `emoji_style` enum('native','twitter','apple','google') DEFAULT 'native',
  `language` varchar(10) DEFAULT 'en',
  `timezone` varchar(50) DEFAULT 'UTC',
  `blocked_users` text DEFAULT NULL,
  `muted_conversations` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_user_settings` (`user_id`),
  KEY `idx_updated_at` (`updated_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- MESSAGE REACTIONS TABLE (for future emoji reactions)
-- =====================================================
CREATE TABLE IF NOT EXISTS `tbl_message_reactions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `message_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `reaction` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_user_message_reaction` (`message_id`, `user_id`, `reaction`),
  KEY `idx_message_reactions` (`message_id`),
  KEY `idx_user_reactions` (`user_id`),
  KEY `idx_reaction_type` (`reaction`),
  FOREIGN KEY (`message_id`) REFERENCES `tbl_chat` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- CHAT GROUPS TABLE (for future group chat support)
-- =====================================================
CREATE TABLE IF NOT EXISTS `tbl_chat_groups` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `avatar` varchar(255) DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_active` tinyint(1) DEFAULT '1',
  `max_members` int(11) DEFAULT '50',
  `is_public` tinyint(1) DEFAULT '0',
  `invite_code` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_created_by` (`created_by`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_is_public` (`is_public`),
  KEY `idx_invite_code` (`invite_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- CHAT GROUP MEMBERS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS `tbl_chat_group_members` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `group_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `role` enum('admin','moderator','member') DEFAULT 'member',
  `joined_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_read_message_id` int(11) DEFAULT NULL,
  `is_muted` tinyint(1) DEFAULT '0',
  `is_active` tinyint(1) DEFAULT '1',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_group_member` (`group_id`, `user_id`),
  KEY `idx_group_members` (`group_id`, `is_active`),
  KEY `idx_user_groups` (`user_id`, `is_active`),
  KEY `idx_group_admins` (`group_id`, `role`),
  FOREIGN KEY (`group_id`) REFERENCES `tbl_chat_groups` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- UPDATE EXISTING TABLES FOR EMOJI SUPPORT
-- =====================================================

-- Update existing chat table if it exists
ALTER TABLE `tbl_chat`
CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Add missing columns to existing chat table
ALTER TABLE `tbl_chat`
ADD COLUMN IF NOT EXISTS `is_edited` TINYINT(1) DEFAULT 0 AFTER `is_deleted`,
ADD COLUMN IF NOT EXISTS `edited_at` TIMESTAMP NULL AFTER `is_edited`,
ADD COLUMN IF NOT EXISTS `original_message` TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL AFTER `edited_at`,
ADD COLUMN IF NOT EXISTS `deleted_for` ENUM('none', 'sender', 'both') DEFAULT 'none' AFTER `original_message`;

-- Update message columns for emoji support
ALTER TABLE `tbl_chat`
MODIFY COLUMN `message` TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
MODIFY COLUMN `original_message` TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL;

-- Add performance indexes if they don't exist
ALTER TABLE `tbl_chat`
ADD INDEX IF NOT EXISTS `idx_edited_deleted` (`is_edited`, `is_deleted`, `deleted_for`),
ADD INDEX IF NOT EXISTS `idx_recipient_timestamp` (`recipient_id`, `timestamp`),
ADD INDEX IF NOT EXISTS `idx_sender_timestamp` (`sender_id`, `timestamp`),
ADD INDEX IF NOT EXISTS `idx_conversation` (`sender_id`, `recipient_id`, `timestamp`),
ADD INDEX IF NOT EXISTS `idx_unread_messages` (`recipient_id`, `is_read`, `timestamp`),
ADD INDEX IF NOT EXISTS `idx_message_type` (`message_type`),
ADD INDEX IF NOT EXISTS `idx_deleted_for` (`deleted_for`);

-- Update other existing tables for emoji support
ALTER TABLE `tbl_chat_files`
CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

ALTER TABLE `tbl_chat_files`
MODIFY COLUMN `original_name` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
MODIFY COLUMN `file_path` VARCHAR(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL;

-- Add mime_type column if it doesn't exist
ALTER TABLE `tbl_chat_files`
ADD COLUMN IF NOT EXISTS `mime_type` VARCHAR(100) DEFAULT NULL AFTER `file_type`,
ADD COLUMN IF NOT EXISTS `is_deleted` TINYINT(1) DEFAULT '0' AFTER `mime_type`;

-- Update conversations table
ALTER TABLE `tbl_conversations`
CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

ALTER TABLE `tbl_conversations`
ADD COLUMN IF NOT EXISTS `last_message_preview` VARCHAR(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL AFTER `last_message_id`,
ADD COLUMN IF NOT EXISTS `user1_deleted` TINYINT(1) DEFAULT '0' AFTER `user2_unread_count`,
ADD COLUMN IF NOT EXISTS `user2_deleted` TINYINT(1) DEFAULT '0' AFTER `user1_deleted`,
ADD COLUMN IF NOT EXISTS `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP AFTER `user2_deleted`;

-- Update user status table
ALTER TABLE `tbl_user_status`
CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

ALTER TABLE `tbl_user_status`
MODIFY COLUMN `status` ENUM('online','away','busy','offline') DEFAULT 'offline',
ADD COLUMN IF NOT EXISTS `custom_status` VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL AFTER `status`,
ADD COLUMN IF NOT EXISTS `is_typing_to` INT(11) DEFAULT NULL AFTER `last_activity`,
ADD COLUMN IF NOT EXISTS `typing_started_at` TIMESTAMP NULL DEFAULT NULL AFTER `is_typing_to`;

-- Update notifications table
ALTER TABLE `tbl_chat_notifications`
CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

ALTER TABLE `tbl_chat_notifications`
MODIFY COLUMN `notification_type` ENUM('message','mention','system','file','image') DEFAULT 'message',
ADD COLUMN IF NOT EXISTS `title` VARCHAR(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL AFTER `notification_type`,
ADD COLUMN IF NOT EXISTS `content` TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL AFTER `title`,
ADD COLUMN IF NOT EXISTS `is_clicked` TINYINT(1) DEFAULT '0' AFTER `read_at`,
ADD COLUMN IF NOT EXISTS `clicked_at` TIMESTAMP NULL DEFAULT NULL AFTER `is_clicked`,
ADD COLUMN IF NOT EXISTS `expires_at` TIMESTAMP NULL DEFAULT NULL AFTER `created_at`;

-- Update settings table
ALTER TABLE `tbl_chat_settings`
CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

ALTER TABLE `tbl_chat_settings`
ADD COLUMN IF NOT EXISTS `email_notifications` TINYINT(1) DEFAULT '0' AFTER `desktop_notifications`,
ADD COLUMN IF NOT EXISTS `show_read_receipts` TINYINT(1) DEFAULT '1' AFTER `show_online_status`,
ADD COLUMN IF NOT EXISTS `show_typing_indicator` TINYINT(1) DEFAULT '1' AFTER `show_read_receipts`,
ADD COLUMN IF NOT EXISTS `message_preview_length` INT(11) DEFAULT '50' AFTER `auto_delete_messages`,
ADD COLUMN IF NOT EXISTS `font_size` ENUM('small','medium','large') DEFAULT 'medium' AFTER `theme`,
ADD COLUMN IF NOT EXISTS `emoji_style` ENUM('native','twitter','apple','google') DEFAULT 'native' AFTER `font_size`,
ADD COLUMN IF NOT EXISTS `language` VARCHAR(10) DEFAULT 'en' AFTER `emoji_style`,
ADD COLUMN IF NOT EXISTS `timezone` VARCHAR(50) DEFAULT 'UTC' AFTER `language`,
ADD COLUMN IF NOT EXISTS `blocked_users` TEXT DEFAULT NULL AFTER `timezone`,
ADD COLUMN IF NOT EXISTS `muted_conversations` TEXT DEFAULT NULL AFTER `blocked_users`;

-- =====================================================
-- UPDATE USERS TABLE FOR EMOJI SUPPORT
-- =====================================================

-- Update users table name fields for emoji support (if table exists)
ALTER TABLE `tbl_users`
MODIFY COLUMN `fname` VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
MODIFY COLUMN `lname` VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- =====================================================
-- PERFORMANCE OPTIMIZATION VIEWS
-- =====================================================

-- Create view for active conversations
CREATE OR REPLACE VIEW `view_active_conversations` AS
SELECT
    c.id,
    c.user1_id,
    c.user2_id,
    c.last_message_id,
    c.last_message_preview,
    c.last_activity,
    c.user1_unread_count,
    c.user2_unread_count,
    u1.fname as user1_fname,
    u1.lname as user1_lname,
    u1.image as user1_image,
    u2.fname as user2_fname,
    u2.lname as user2_lname,
    u2.image as user2_image,
    s1.status as user1_status,
    s1.last_seen as user1_last_seen,
    s2.status as user2_status,
    s2.last_seen as user2_last_seen
FROM tbl_conversations c
LEFT JOIN tbl_users u1 ON c.user1_id = u1.id
LEFT JOIN tbl_users u2 ON c.user2_id = u2.id
LEFT JOIN tbl_user_status s1 ON c.user1_id = s1.user_id
LEFT JOIN tbl_user_status s2 ON c.user2_id = s2.user_id
WHERE c.user1_deleted = 0 AND c.user2_deleted = 0
ORDER BY c.last_activity DESC;

-- Create view for recent messages
CREATE OR REPLACE VIEW `view_recent_messages` AS
SELECT
    m.id,
    m.sender_id,
    m.recipient_id,
    m.message,
    m.message_type,
    m.timestamp,
    m.is_read,
    m.is_edited,
    m.deleted_for,
    u.fname as sender_fname,
    u.lname as sender_lname,
    u.image as sender_image,
    f.original_name as file_name,
    f.file_path,
    f.file_size,
    f.file_type
FROM tbl_chat m
LEFT JOIN tbl_users u ON m.sender_id = u.id
LEFT JOIN tbl_chat_files f ON m.id = f.message_id
WHERE m.deleted_for != 'both'
ORDER BY m.timestamp DESC;

-- =====================================================
-- STORED PROCEDURES FOR COMMON OPERATIONS
-- =====================================================

DELIMITER //

-- Procedure to get conversation messages
CREATE PROCEDURE IF NOT EXISTS GetConversationMessages(
    IN p_user1_id INT,
    IN p_user2_id INT,
    IN p_limit INT,
    IN p_offset INT
)
BEGIN
    SELECT
        m.id,
        m.sender_id,
        m.recipient_id,
        m.message,
        m.message_type,
        m.timestamp,
        m.is_read,
        m.is_edited,
        m.edited_at,
        m.deleted_for,
        u.fname,
        u.lname,
        u.image,
        f.original_name,
        f.file_path,
        f.file_size,
        f.file_type
    FROM tbl_chat m
    LEFT JOIN tbl_users u ON m.sender_id = u.id
    LEFT JOIN tbl_chat_files f ON m.id = f.message_id
    WHERE ((m.sender_id = p_user1_id AND m.recipient_id = p_user2_id)
           OR (m.sender_id = p_user2_id AND m.recipient_id = p_user1_id))
    AND (m.deleted_for != 'both' AND
         (m.deleted_for != 'sender' OR m.sender_id != p_user1_id))
    ORDER BY m.timestamp ASC
    LIMIT p_limit OFFSET p_offset;
END //

-- Procedure to mark messages as read
CREATE PROCEDURE IF NOT EXISTS MarkMessagesAsRead(
    IN p_user_id INT,
    IN p_sender_id INT
)
BEGIN
    UPDATE tbl_chat
    SET is_read = 1, read_at = NOW()
    WHERE recipient_id = p_user_id
    AND sender_id = p_sender_id
    AND is_read = 0;

    -- Update conversation unread count
    UPDATE tbl_conversations
    SET user1_unread_count = CASE
        WHEN user1_id = p_user_id THEN 0
        ELSE user1_unread_count
    END,
    user2_unread_count = CASE
        WHEN user2_id = p_user_id THEN 0
        ELSE user2_unread_count
    END
    WHERE (user1_id = p_user_id AND user2_id = p_sender_id)
    OR (user1_id = p_sender_id AND user2_id = p_user_id);
END //

-- Procedure to update conversation
CREATE PROCEDURE IF NOT EXISTS UpdateConversation(
    IN p_user1_id INT,
    IN p_user2_id INT,
    IN p_message_id INT,
    IN p_message_preview VARCHAR(200)
)
BEGIN
    INSERT INTO tbl_conversations (user1_id, user2_id, last_message_id, last_message_preview, last_activity)
    VALUES (LEAST(p_user1_id, p_user2_id), GREATEST(p_user1_id, p_user2_id), p_message_id, p_message_preview, NOW())
    ON DUPLICATE KEY UPDATE
    last_message_id = p_message_id,
    last_message_preview = p_message_preview,
    last_activity = NOW(),
    user1_unread_count = CASE
        WHEN user1_id != p_user1_id THEN user1_unread_count + 1
        ELSE user1_unread_count
    END,
    user2_unread_count = CASE
        WHEN user2_id != p_user1_id THEN user2_unread_count + 1
        ELSE user2_unread_count
    END;
END //

DELIMITER ;

-- =====================================================
-- CLEANUP AND MAINTENANCE
-- =====================================================

-- Create event to clean up old notifications (if events are enabled)
-- SET GLOBAL event_scheduler = ON;

-- CREATE EVENT IF NOT EXISTS cleanup_old_notifications
-- ON SCHEDULE EVERY 1 DAY
-- DO
--   DELETE FROM tbl_chat_notifications
--   WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY)
--   AND is_read = 1;

-- =====================================================
-- INITIAL DATA SETUP
-- =====================================================

-- Insert default chat settings for existing users
INSERT IGNORE INTO tbl_chat_settings (user_id, notifications_enabled, sound_enabled, desktop_notifications)
SELECT id, 1, 1, 1 FROM tbl_users;

-- Insert default user status for existing users
INSERT IGNORE INTO tbl_user_status (user_id, status, last_seen, last_activity)
SELECT id, 'offline', NOW(), NOW() FROM tbl_users;

-- =====================================================
-- COMPLETION MESSAGE
-- =====================================================

SELECT 'Chatbox database schema setup completed successfully!' as Status;
