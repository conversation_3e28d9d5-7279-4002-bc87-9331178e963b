<style>
img.pic {
    display: block;
    max-width: 100%;
    max-height: 100%;
}

.preview {
    overflow: hidden;
    width: 160px;
    height: 160px;
    margin: 10px;
    border: 1px solid red;
}

.modal-lg {
    max-width: 1000px !important;
}
</style>
<?php
    $q = "SELECT * FROM tbl_users WHERE id = ? OR empnum = ? ";
    $stmt = $connection->prepare($q);
    $stmt -> bind_param("ii", $_SESSION['id'],$_SESSION['empnum']);
    $stmt -> execute();
    $result = mysqli_stmt_get_result($stmt);
    while ($row = mysqli_fetch_assoc($result)) {
?>

<div class="text-muted bg-light shadow p-3" style="border-radius: 5px;">
    <center>
        <!-- PROFILE IMAGE -->
        <form action="" enctype="multipart/form-data" method="post">
            <div class="upload">
                <?= (($row['image'] == '') ? '<img src="images/profile/thumbs/profile.png" class="pt-2 img-fluid"
         width="150px" alt="">': '<img src="images/profile/thumbs/'.$row['image'].'" class="rounded-circle pt-2 img-fluid" width="150px" alt="">');?>
                <div class="round">
                    <input type="hidden" name="empnum" value="<?php echo $_SESSION['empnum']; ?>">
                    <input type="file" name='crop_image' class='pointer crop_image file' id='upload_image'>
                    <i class="fa fa-camera" style="color: #fff;"></i>
                </div>
            </div>
        </form>
        <!-- PROFILE IMAGE -->
        <hr>
        <strong>
            <h4><?= ucwords(strtolower($row['lname'])).", ".$row['fname']." ".(($row['mname'] == "") ?
            "":ucwords(strtolower($row['mname'])))."".(($row['extname'] == "") ?"":" ".ucwords(strtolower($row['extname'])));?>
            </h4>
        </strong>
        <?=(($row['position']=='')?$row['email']:$row['position']);?><br>
        <?=(($row['position']=='')?'':$row['email']);?><br>

        <?php include "edit_notification.php"; ?>

        <span type="button" class="badge text-bg-light text-muted" data-bs-toggle="modal"
            data-bs-target="#Modalhistory">promotion history</span> |
        <span type="button" class="badge text-bg-light text-muted" data-bs-toggle="modal"
            data-bs-target="#ModalEmpHistory">employment history</span>
    </center><br>

    <span>
        <div class="card">
            <div class="card-body">

                <!-- prom_deleted -->
                <?php
                if(isset($_SESSION['prom_deleted'])): ?>
                <div class="alert <?php echo $_SESSION['alert-class'];?>" role="alert">
                    <center>
                        <?php
                        echo $_SESSION['prom_deleted'];
                        unset($_SESSION['prom_deleted']);
                        unset($_SESSION['alert-class']);
?>
                    </center>
                </div>
                <?php endif; ?>

                <!-- Employment station_updated -->
                <?php
                if(isset($_SESSION['station_updated'])): ?>
                <div class="alert <?php echo $_SESSION['alert-class'];?>" role="alert">
                    <center>
                        <?php
                        echo $_SESSION['station_updated'];
                        unset($_SESSION['station_updated']);
                        unset($_SESSION['alert-class']);
                        ?>
                    </center>
                </div>
                <?php endif; ?>


                <!-- Employment Status Updated -->
                <?php
                if(isset($_SESSION['stat_updated'])): ?>
                <div class="alert <?php echo $_SESSION['alert-class'];?>" role="alert">
                    <center>
                        <?php
                        echo $_SESSION['stat_updated'];
                        unset($_SESSION['stat_updated']);
                        unset($_SESSION['alert-class']);
                        ?>
                    </center>
                </div>
                <?php endif; ?>

                <p>Employee Number: <strong><?= $row['empnum'];?></strong></p>
                <p>Station: <strong><?php 
                    $str = $_SESSION['station'];  
                    $school = explode(" - ", $str);
                    // Check if array has the expected index before accessing it
                    echo (isset($school[1])) ? $school[1] : $str; 
                ?> <i class="fa fa-pencil text-secondary" style="cursor:pointer;" aria-hidden="true"
                            data-bs-toggle="modal"
                            data-bs-target="#edit_stationModal<?= $row['empnum'];?>"></i></strong></p>
                <p>Date of Original Appointment: <strong><?= $row['date_orig_appointment'];?></strong></p>
                <p>Date of Latest Promotion: <strong><?= $row['date_last_promotion'];?></strong></p>
                <p>Employment Status: <strong><?= $row['emp_status'];?></strong></p>
                <?php
                if(in_array($_SESSION['usertype'], [1, 2, 3, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]) OR (in_array($_SESSION['empnum'], [6095315, 6285490]))){
                    echo '<p>Compensatory Overtime Credits: <span class="text-muted small" style="cursor: pointer;" onclick="openCOCModal(\''.$row['empnum'].'\', \''.$row['lname'].', '.$row['fname'].' '.$row['mname'].' '.$row['extname'].'\')"><em>details</em></span></p>
                    <p>Leave Credits: <span class="text-muted small" style="cursor: pointer;" onclick="openLeaveCreditsModal(\''.$row['empnum'].'\', \''.$row['lname'].', '.$row['fname'].' '.$row['mname'].' '.$row['extname'].'\')"><em>details</em></span></p>';
                }else{
                    echo '<p>Leave/Service Credits: <span class="text-muted small" style="cursor: pointer;"
                        onclick="openServiceCreditsModal(\''.$row['empnum'].'\', \''.$row['lname'].', '.$row['fname'].' '.$row['mname'].' '.$row['extname'].'\')"><em>details</em></span>
                    </p>';
                }
                ?>
                <hr>
                <div style="display: flex;" class="float-end">
                    <button type="btn" class="btn btn-sm btn-outline-secondary" data-bs-toggle="modal"
                        data-bs-target="#Modalupdate"
                        style="--bs-btn-padding-y: .25rem; --bs-btn-padding-x: .5rem; --bs-btn-font-size: .75rem;">update</button>
                </div>
            </div>
        </div>
</div>
<!-- End -->
</span>

<!-- Modal -->
<div class="modal fade" id="edit_stationModal<?= $row['empnum'];?>" tabindex="-1"
    aria-labelledby="edit_stationModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h1 class="modal-title fs-5" id="edit_stationModalLabel">Edit Office/Station</h1>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="update_station.php" method="post" enctype="multipart/form-data">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="sch" class="col-sm-3 control-label">Office/Station*</label>
                        <div class="col">
                            <select id="school-list" name="user_school" class="form-control" required>
                                <option value="<?php $str = $_SESSION['station'];  
                                            $school = explode(" - ", $str);
                                            echo $school[1]; ?>" required selected><?php $str = $_SESSION['station'];  
                                            $school = explode(" - ", $str);
                                            echo $school[1]; ?></option>
                                <option value="288 - Bais City Division">Bais City Division</option>
                                <option value="120071 - Amalao Elementary School">Amalao Elementary School</option>
                                <option value="120072 - Bais City Pilot School">Bais City Pilot School</option>
                                <option value="120082 - Bais City South Central School">Bais City South Central School
                                </option>
                                <option value="237505 - Bais City Special Science Elementary School">Bais City Special
                                    Science Elementary School</option>
                                <option value="237503 - Bais City West Elementary School">Bais City West Elementary
                                    School</option>
                                <option value="120083 - Basak Elementary School">Basak Elementary School</option>
                                <option value="120084 - Biso Elementary School">Biso Elementary School</option>
                                <option value="120085 - Cabanlutan Elementary School">Cabanlutan Elementary School
                                </option>
                                <option value="120086 - Calasgaan Elementary School">Calasgaan Elementary School
                                </option>
                                <option value="120088 - Cambagahan Elementary School">Cambagahan Elementary School
                                </option>
                                <option value="120089 - Cambaguio Elementary School">Cambaguio Elementary School
                                </option>
                                <option value="120073 - Cambanjao Elementary School">Cambanjao Elementary School
                                </option>
                                <option value="237501 - Cambayungon Elementary School">Cambayungon Elementary School
                                </option>
                                <option value="120074 - Cambuilao Elementary School">Cambuilao Elementary School
                                </option>
                                <option value="120075 - Cansanag Elementary School">Cansanag Elementary School</option>
                                <option value="120090 - Cipriano Alcala Memorial Elementary School">Cipriano Alcala
                                    Memorial Elementary School</option>
                                <option value="120076 - Dansulan Elementary School">Dansulan Elementary School</option>
                                <option value="120091 - Dawis Elementary School">Dawis Elementary School</option>
                                <option value="120087 - Dr. Ernesto M. Yared Sr. Memorial Elementary School">Dr. Ernesto
                                    Yared Elementary School</option>
                                <option value="237504 - Katacgahan Elementary School">Katacgahan Elementary School
                                </option>
                                <option value="120092 - Lonoy Elementary School">Lonoy Elementary School</option>
                                <option value="120093 - Looc Elementary School">Looc Elementary School</option>
                                <option value="120094 - Lowason Elementary School">Lowason Elementary School</option>
                                <option value="120095 - Mabunao Elementary School">Mabunao Elementary School</option>
                                <option value="120096 - Mangganay Elementary School">Mangganay Elementary School
                                </option>
                                <option value="120097 - Mansangaban Elementary School">Mansangaban Elementary School
                                </option>
                                <option value="120098 - Okiot Elementary School">Okiot Elementary School</option>
                                <option value="120077 - Olympia Elementary School">Olympia Elementary School</option>
                                <option value="120099 - Palangging Elementary School">Palangging Elementary School
                                </option>
                                <option value="120100 - Palaypay Elementary School">Palaypay Elementary School</option>
                                <option value="120101 - Panalaan Elementary School">Panalaan Elementary School</option>
                                <option value="120102 - Panam-angan Elementary School">Panam-angan Elementary School
                                </option>
                                <option value="120078 - Praxevilla Elementary School">Praxevilla Elementary School
                                </option>
                                <option value="120103 - Sab-ahan Elementary School">Sab-ahan Elementary School</option>
                                <option value="120104 - San Vicente Elementary School">San Vicente Elementary School
                                </option>
                                <option value="120079 - Sto. Tomas Elementary School">Sto. Tomas Elementary School
                                </option>
                                <option value="120105 - Tabuac Elementary School">Tabuac Elementary School</option>
                                <option value="120106 - Tacalan Elementary School">Tacalan Elementary School</option>
                                <option value="120080 - Tagpo Elementary School">Tagpo Elementary School</option>
                                <option value="237502 - Talungon Elementary School">Talungon Elementary School</option>
                                <option value="120081 - Tangculogan Elementary School">Tangculogan Elementary School
                                </option>
                                <option value="303212 - Bais City National High School">Bais City National High School
                                </option>
                                <option value="303216 - Bais City National Science High School">Bais City National
                                    Science High School</option>
                                <option value="323011 - Bais City Olympia National High School">Bais City Olympia
                                    National High School</option>
                                <option value="323001 - Cabugan National High School">Cabugan National High School
                                </option>
                                <option value="323004 - Cambagahan National High School">Cambagahan National High School
                                </option>
                                <option value="303214 - Dodong Escaño Memorial High School">Dodong Escaño Memorial High
                                    School</option>
                                <option value="323003 - Governor Julian L. Teves Memorial High School">Governor Julian
                                    L. Teves Memorial High School</option>
                                <option value="323002 - Javier Laxina I Memorial High School">Javier Laxina I Memorial
                                    High School</option>
                                <option value="303213 - Josefina T. Valencia National High School">Josefina T. Valencia
                                    National High School</option>
                                <option value="303215 - Lonoy National High School">Lonoy National High School</option>
                                <option value="323010 - Lucila C. Yared National High School">Lucila C. Yared National
                                    High School</option>
                                <option value="323007 - Mansangaban National High School">Mansangaban National High
                                    School</option>
                                <option value="323005 - Manuel L. Teves Memorial High School">Manuel L. Teves Memorial
                                    High School</option>
                                <option value="323012 - Mayor Praxedes P. Villanueva II MHS">Mayor Praxedes P.
                                    Villanueva II MHS</option>
                                <option value="323008 - Okiot National High School">Okiot National High School</option>
                                <option value="323006 - Sto. Thomas National High School">Sto. Thomas National High
                                    School</option>
                                <option value="323009 - Tagpo National High School">Tagpo National High School</option>
                                <option value="323013 - Tangculogan High School">Tangculogan High School</option>
                                <option value="ALS - Alternative Learning System">Alternative Learning System (ALS)
                                </option>
                            </select>
                            <div class="valid-feedback">Valid.</div>
                            <div class="invalid-feedback"><i class="fa fa-exclamation"></i> Required.</div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <input type="hidden" name="empnum" value="<?= $row['empnum'];?>">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-primary">Save changes</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Service Credits Modal -->
<div class="modal" id="serviceCreditsModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Records: Leave of Absences<br /><span class="small"
                        style="font-weight: 100;"><em>(Under Commonwealth Act. No. 220, as amended)</em></span></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Name: <strong><span id="serviceCreditsEmployeeName"></span></strong><br />
                    Station: <strong><span id="serviceCreditsEmployeeSchool"></span></strong></p>
                <input type="hidden" id="serviceCreditsEmpNum" name="empnum">
                <input type="hidden" id="serviceCreditsFullName" name="fullName">
                <div class="mb-3">
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped" id="serviceCreditsTable">
                            <thead class="table-secondary">
                                <tr class="text-center">
                                    <th>GRANT OF LEAVE/SERVICE CREDITS</th>
                                    <th>NO. OF SERVICE CREDITS</th>
                                    <th>NO. OF DAYS ABSENT</th>
                                    <th>BALANCE</th>
                                </tr>
                            </thead>
                            <tbody id="serviceCreditsBody">
                                <!-- Data will be loaded here dynamically -->
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" onclick="printServiceCredits()">Print</button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function openServiceCreditsModal(empnum, fullName) {
    if (!empnum || !fullName) {
        alert('Employee information is missing');
        return;
    }

    // Set employee information in the modal
    document.getElementById('serviceCreditsEmpNum').value = empnum;
    document.getElementById('serviceCreditsFullName').value = fullName;
    document.getElementById('serviceCreditsEmployeeName').textContent = fullName;

    // Fetch school information
    fetch(`/prime-hrm/admin_panel/service_records/fetch_school.php?empnum=${empnum}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('serviceCreditsEmployeeSchool').textContent = data.school || 'N/A';
            } else {
                document.getElementById('serviceCreditsEmployeeSchool').textContent = 'N/A';
            }
        })
        .catch(error => {
            console.error('Error fetching school:', error);
            document.getElementById('serviceCreditsEmployeeSchool').textContent = 'N/A';
        });

    // Fetch service credits data
    fetch(`/prime-hrm/admin_panel/service_records/fetch_service_credits.php?empnum=${empnum}`)
        .then(response => response.json())
        .then(data => {
            const tableBody = document.getElementById('serviceCreditsBody');
            tableBody.innerHTML = '';

            if (data.success && data.records.length > 0) {
                data.records.forEach(record => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${record.grant_of_leave}</td>
                        <td class="text-center">${record.credits_earned}</td>
                        <td class="text-center">${record.days_absent}</td>
                        <td class="text-center">${record.total}</td>
                    `;
                    tableBody.appendChild(row);
                });
            } else {
                tableBody.innerHTML = '<tr><td colspan="4" class="text-center">No records found</td></tr>';
            }
        })
        .catch(error => {
            console.error('Error fetching service credits:', error);
            document.getElementById('serviceCreditsBody').innerHTML =
                '<tr><td colspan="4" class="text-center">Error loading records</td></tr>';
        });

    // Show the modal
    const modal = new bootstrap.Modal(document.getElementById('serviceCreditsModal'));
    modal.show();
}

function printServiceCredits() {
    const empnum = document.getElementById('serviceCreditsEmpNum').value;
    const fullName = document.getElementById('serviceCreditsFullName').value;
    const employeeName = document.getElementById('serviceCreditsEmployeeName').textContent;
    const employeeSchool = document.getElementById('serviceCreditsEmployeeSchool').textContent;

    if (!empnum || !fullName) {
        alert('Employee information is missing');
        return;
    }

    // Create a new window for printing
    const printWindow = window.open('', '_blank', 'width=800,height=600');

    // Get the table data
    const tableBody = document.getElementById('serviceCreditsBody');
    const rows = tableBody.querySelectorAll('tr');

    let tableContent = '';
    rows.forEach(row => {
        const cells = row.querySelectorAll('td');
        if (cells.length >= 4) { // Make sure it's not the "No records found" row
            tableContent += `
                <tr>
                    <td style="border: 1px solid #000; padding: 8px; text-align: center;">${cells[0].textContent}</td>
                    <td style="border: 1px solid #000; padding: 8px; text-align: center;">${cells[1].textContent}</td>
                    <td style="border: 1px solid #000; padding: 8px; text-align: center;">${cells[2].textContent}</td>
                    <td style="border: 1px solid #000; padding: 8px; text-align: center;">${cells[3].textContent}</td>
                </tr>
            `;
        }
    });

    // If no records, show appropriate message
    if (tableContent === '') {
        tableContent =
            '<tr><td colspan="4" style="border: 1px solid #000; padding: 8px; text-align: center;">No records found</td></tr>';
    }

    // Create the print content
    const printContent = `
        <!DOCTYPE html>
        <html>
        <head>
            <title>Leave of Absences Record - ${employeeName}</title>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    margin: 20px;
                    font-size: 12px;
                }
                .header {
                    text-align: center;
                    margin-bottom: 30px;
                }
                .title {
                    font-size: 16px;
                    font-weight: bold;
                    margin-bottom: 5px;
                }
                .subtitle {
                    font-size: 12px;
                    font-style: italic;
                    margin-bottom: 20px;
                }
                .employee-info {
                    margin-bottom: 20px;
                }
                .employee-info p {
                    margin: 5px 0;
                }
                table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-top: 20px;
                }
                th {
                    background-color: #f8f9fa;
                    border: 1px solid #000;
                    padding: 10px;
                    text-align: center;
                    font-weight: bold;
                }
                td {
                    border: 1px solid #000;
                    padding: 8px;
                    text-align: center;
                }
                @media print {
                    body { margin: 0; }
                    .no-print { display: none; }
                }
            </style>
        </head>
        <body>
            <div class="header">
                <div class="title">Records: Leave of Absences</div>
                <div class="subtitle">(Under Commonwealth Act. No. 220, as amended)</div>
            </div>

            <div class="employee-info">
                <p><strong>Name:</strong> ${employeeName}</p>
                <p><strong>Station:</strong> ${employeeSchool}</p>
            </div>

            <table>
                <thead>
                    <tr>
                        <th>GRANT OF LEAVE/SERVICE CREDITS</th>
                        <th>NO. OF SERVICE CREDITS</th>
                        <th>NO. OF DAYS ABSENT</th>
                        <th>BALANCE</th>
                    </tr>
                </thead>
                <tbody>
                    ${tableContent}
                </tbody>
            </table>

            <div class="no-print" style="margin-top: 20px; text-align: center;">
                <button onclick="window.print()" style="padding: 10px 20px; font-size: 14px;">Print</button>
                <button onclick="window.close()" style="padding: 10px 20px; font-size: 14px; margin-left: 10px;">Close</button>
            </div>
        </body>
        </html>
    `;

    // Write content to the new window
    printWindow.document.write(printContent);
    printWindow.document.close();

    // Focus on the new window and trigger print dialog
    printWindow.focus();

    // Auto-print after a short delay to ensure content is loaded
    setTimeout(() => {
        printWindow.print();
    }, 500);
}

// =====================================================
// COC Modal Functions
// =====================================================

function openCOCModal(empnum, fullName) {
    // Set employee information
    document.getElementById('cocEmpNum').value = empnum;
    document.getElementById('cocEmployeeName').textContent = fullName;

    // Load COC data
    loadCOCData(empnum);

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('cocModal'));
    modal.show();
}

function loadCOCData(empnum) {
    fetch(`/prime-hrm/admin_panel/service_records/coc_operations.php?operation=fetch&empnum=${empnum}`)
        .then(response => response.json())
        .then(data => {
            const tableBody = document.getElementById('cocTableBody');
            tableBody.innerHTML = '';

            if (data.success && data.records.length > 0) {
                data.records.forEach(record => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${record.date_title_coc_earned}</td>
                        <td class="text-center">${parseFloat(record.total_coc_earned).toFixed(2)}</td>
                        <td class="text-center">${formatDateToManila(record.date_acquired)}</td>
                        <td class="text-center">${formatDateToManila(record.date_expired)}</td>
                        <td class="text-center">${parseFloat(record.cto_used).toFixed(2)}</td>
                        <td class="text-center">${parseFloat(record.number_coc_used).toFixed(2)}</td>
                        <td class="text-center">${parseFloat(record.remaining_coc).toFixed(2)}</td>
                        <td>${record.remarks || ''}</td>
                    `;
                    tableBody.appendChild(row);
                });
            } else {
                tableBody.innerHTML = '<tr><td colspan="8" class="text-center">No COC records found</td></tr>';
            }
        })
        .catch(error => {
            console.error('Error fetching COC records:', error);
            document.getElementById('cocTableBody').innerHTML =
                '<tr><td colspan="8" class="text-center">Error loading COC records</td></tr>';
        });
}

// =====================================================
// Leave Credits Modal Functions
// =====================================================

function openLeaveCreditsModal(empnum, fullName) {
    // Set employee information
    document.getElementById('leaveCreditsEmpNum').value = empnum;
    document.getElementById('leaveCreditsEmployeeName').textContent = fullName;

    // Load Leave Credits data
    loadLeaveCreditsData(empnum);

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('leaveCreditsModal'));
    modal.show();
}

function loadLeaveCreditsData(empnum) {
    fetch(`/prime-hrm/admin_panel/service_records/leave_credits_operations.php?operation=fetch&empnum=${empnum}`)
        .then(response => response.json())
        .then(data => {
            const tableBody = document.getElementById('leaveCreditsTableBody');
            tableBody.innerHTML = '';

            if (data.success && data.records.length > 0) {
                data.records.forEach(record => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td class="text-center">${parseFloat(record.vl_earned).toFixed(2)}</td>
                        <td class="text-center">${parseFloat(record.sl_earned).toFixed(2)}</td>
                        <td class="text-center">${formatDateToManila(record.period_from)}</td>
                        <td class="text-center">${formatDateToManila(record.period_to)}</td>
                        <td class="text-center">${parseFloat(record.vl_taken_with_pay).toFixed(2)}</td>
                        <td class="text-center">${parseFloat(record.sl_taken_with_pay).toFixed(2)}</td>
                        <td class="text-center">${parseFloat(record.leaves_without_pay).toFixed(2)}</td>
                        <td class="text-center">${parseFloat(record.undertime_vl).toFixed(2)}</td>
                        <td class="text-center">${parseFloat(record.undertime_sl).toFixed(2)}</td>
                        <td class="text-center">${parseFloat(record.undertime_without_pay).toFixed(2)}</td>
                        <td class="text-center">${parseFloat(record.balance_vl).toFixed(2)}</td>
                        <td class="text-center">${parseFloat(record.balance_sl).toFixed(2)}</td>
                        <td>${record.remarks || ''}</td>
                    `;
                    tableBody.appendChild(row);
                });
            } else {
                tableBody.innerHTML =
                    '<tr><td colspan="13" class="text-center">No leave credits records found</td></tr>';
            }
        })
        .catch(error => {
            console.error('Error fetching leave credits records:', error);
            document.getElementById('leaveCreditsTableBody').innerHTML =
                '<tr><td colspan="13" class="text-center">Error loading leave credits records</td></tr>';
        });
}

// =====================================================
// Date Formatting Function
// =====================================================

function formatDateToManila(dateString) {
    if (!dateString) return '';

    try {
        const date = new Date(dateString + 'T00:00:00');

        const options = {
            timeZone: 'Asia/Manila',
            year: 'numeric',
            month: 'short',
            day: '2-digit'
        };

        const formatter = new Intl.DateTimeFormat('en-US', options);
        const parts = formatter.formatToParts(date);

        const month = parts.find(part => part.type === 'month').value;
        const day = parts.find(part => part.type === 'day').value;
        const year = parts.find(part => part.type === 'year').value;

        return `${month}. ${day}, ${year}`;
    } catch (error) {
        console.error('Date formatting error:', error);
        return dateString;
    }
}

// =====================================================
// Print Functions
// =====================================================

function printCOCRecords() {
    const empnum = document.getElementById('cocEmpNum').value;
    const fullName = document.getElementById('cocEmployeeName').textContent;

    window.open(
        `/prime-hrm/admin_panel/service_records/print_coc.php?empnum=${empnum}&name=${encodeURIComponent(fullName)}&hide_signature=1`,
        '_blank');
}

function printLeaveCreditsRecords() {
    const empnum = document.getElementById('leaveCreditsEmpNum').value;
    const fullName = document.getElementById('leaveCreditsEmployeeName').textContent;

    window.open(
        `/prime-hrm/admin_panel/service_records/print_leave_credits.php?empnum=${empnum}&name=${encodeURIComponent(fullName)}&hide_signature=1`,
        '_blank');
}
</script>

<!-- COC Modal -->
<div class="modal fade" id="cocModal" tabindex="-1" aria-labelledby="cocModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="cocModalLabel">Summary of Compensatory Overtime Credit<br />
                    <span class="small" style="font-weight: 100;"><em>(Under Commonwealth Act. No. 220, as
                            amended)</em></span>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>

            <!-- Fixed Print Button Section -->
            <div class="border-bottom px-3 py-2 bg-light">
                <div class="d-flex gap-2 justify-content-center">
                    <button type="button" class="btn btn-primary btn-sm" onclick="printCOCRecords()">
                        <i class="fa fa-print"></i> Print
                    </button>
                </div>
            </div>

            <!-- Scrollable Content Area -->
            <div class="modal-body" style="max-height: 60vh; overflow-y: auto; padding-top: 1rem;">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <p><strong>Employee:</strong> <span id="cocEmployeeName"></span></p>
                        <input type="hidden" id="cocEmpNum" name="empnum">
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead class="table-secondary">
                            <tr class="text-center">
                                <th>Date and Title COC Earned</th>
                                <th>Total COC Earned</th>
                                <th>Date Acquired</th>
                                <th>Date Expired</th>
                                <th>CTO Used</th>
                                <th>Number COC Used</th>
                                <th>Remaining COC</th>
                                <th>Remarks</th>
                            </tr>
                        </thead>
                        <tbody id="cocTableBody">
                            <tr>
                                <td colspan="8" class="text-center">
                                    <em>COC table content will be loaded automatically</em>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Leave Credits Modal -->
<div class="modal fade" id="leaveCreditsModal" tabindex="-1" aria-labelledby="leaveCreditsModalLabel"
    aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="leaveCreditsModalLabel">Records of Leave of Absences<br />
                    <span class="small" style="font-weight: 100;"><em>(Under Commonwealth Act. No. 220, as
                            amended)</em></span>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>

            <!-- Fixed Print Button Section -->
            <div class="border-bottom px-3 py-2 bg-light">
                <div class="d-flex gap-2 justify-content-center">
                    <button type="button" class="btn btn-primary btn-sm" onclick="printLeaveCreditsRecords()">
                        <i class="fa fa-print"></i> Print
                    </button>
                </div>
            </div>

            <!-- Scrollable Content Area -->
            <div class="modal-body" style="max-height: 60vh; overflow-y: auto; padding-top: 1rem;">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <p><strong>Employee:</strong> <span id="leaveCreditsEmployeeName"></span></p>
                        <input type="hidden" id="leaveCreditsEmpNum" name="empnum">
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead class="table-secondary">
                            <tr class="text-center">
                                <th rowspan="3">VL Earned</th>
                                <th rowspan="3">SL Earned</th>
                                <th rowspan="3">From</th>
                                <th rowspan="3">To</th>
                                <th colspan="3">Leaves Taken</th>
                                <th colspan="3">Undertime</th>
                                <th colspan="2">Balance</th>
                                <th rowspan="3">REMARKS</th>
                            </tr>
                            <tr>
                                <th colspan="2">With Pay</th>
                                <th rowspan="2">Without Pay</th>
                                <th rowspan="2">VL</th>
                                <th rowspan="2">SL</th>
                                <th rowspan="2">Without Pay</th>
                                <th rowspan="2">VL</th>
                                <th rowspan="2">SL</th>
                            </tr>
                            <tr>
                                <th>VL</th>
                                <th>SL</th>
                            </tr>
                        </thead>
                        <tbody id="leaveCreditsTableBody">
                            <tr>
                                <td colspan="13" class="text-center">
                                    <em>Leave Credits table content will be loaded automatically</em>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<?php }; ?>