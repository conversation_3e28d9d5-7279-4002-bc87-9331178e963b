<?php
session_start();
header('Content-Type: text/html; charset=utf-8');
include '../db/dbconfig.php';

// Set UTF-8 encoding for database connection
mysqli_set_charset($connection, 'utf8mb4');

echo "<h2>Chat System Debug Information</h2>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.success { color: green; }
.error { color: red; }
.info { color: blue; }
.section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
table { border-collapse: collapse; width: 100%; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
</style>";

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo "<div class='error'>❌ User not logged in. Session user_id not found.</div>";
    echo "<div class='info'>Current session data: " . print_r($_SESSION, true) . "</div>";
    exit;
}

$user_id = $_SESSION['user_id'];
echo "<div class='success'>✅ User logged in with ID: $user_id</div>";

// Test 1: Database Connection
echo "<div class='section'>";
echo "<h3>1. Database Connection Test</h3>";
if ($connection) {
    echo "<div class='success'>✅ Database connected successfully</div>";
    echo "<div class='info'>Connection charset: " . mysqli_character_set_name($connection) . "</div>";
} else {
    echo "<div class='error'>❌ Database connection failed: " . mysqli_connect_error() . "</div>";
    exit;
}
echo "</div>";

// Test 2: Check if tables exist
echo "<div class='section'>";
echo "<h3>2. Table Structure Check</h3>";
$tables = ['tbl_chat', 'tbl_users', 'tbl_chat_files', 'tbl_conversations'];
foreach ($tables as $table) {
    $result = mysqli_query($connection, "SHOW TABLES LIKE '$table'");
    if (mysqli_num_rows($result) > 0) {
        echo "<div class='success'>✅ Table '$table' exists</div>";
        
        // Check table structure for tbl_chat
        if ($table === 'tbl_chat') {
            $columns = mysqli_query($connection, "DESCRIBE $table");
            echo "<details><summary>View $table structure</summary>";
            echo "<table><tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
            while ($col = mysqli_fetch_assoc($columns)) {
                echo "<tr>";
                echo "<td>{$col['Field']}</td>";
                echo "<td>{$col['Type']}</td>";
                echo "<td>{$col['Null']}</td>";
                echo "<td>{$col['Key']}</td>";
                echo "<td>{$col['Default']}</td>";
                echo "</tr>";
            }
            echo "</table></details>";
        }
    } else {
        echo "<div class='error'>❌ Table '$table' does not exist</div>";
    }
}
echo "</div>";

// Test 3: Check for chat messages
echo "<div class='section'>";
echo "<h3>3. Chat Messages Check</h3>";
$query = "SELECT COUNT(*) as total FROM tbl_chat";
$result = mysqli_query($connection, $query);
if ($result) {
    $row = mysqli_fetch_assoc($result);
    echo "<div class='info'>Total messages in database: {$row['total']}</div>";
    
    if ($row['total'] > 0) {
        // Check messages for current user
        $user_messages = mysqli_query($connection, "
            SELECT COUNT(*) as count 
            FROM tbl_chat 
            WHERE sender_id = $user_id OR recipient_id = $user_id
        ");
        $user_count = mysqli_fetch_assoc($user_messages);
        echo "<div class='info'>Messages involving user $user_id: {$user_count['count']}</div>";
        
        // Show recent messages
        $recent = mysqli_query($connection, "
            SELECT c.*, u1.fname as sender_name, u2.fname as recipient_name 
            FROM tbl_chat c
            LEFT JOIN tbl_users u1 ON c.sender_id = u1.id
            LEFT JOIN tbl_users u2 ON c.recipient_id = u2.id
            WHERE c.sender_id = $user_id OR c.recipient_id = $user_id
            ORDER BY c.timestamp DESC 
            LIMIT 5
        ");
        
        if (mysqli_num_rows($recent) > 0) {
            echo "<h4>Recent Messages:</h4>";
            echo "<table>";
            echo "<tr><th>ID</th><th>From</th><th>To</th><th>Message</th><th>Time</th><th>Read</th></tr>";
            while ($msg = mysqli_fetch_assoc($recent)) {
                echo "<tr>";
                echo "<td>{$msg['id']}</td>";
                echo "<td>{$msg['sender_name']} ({$msg['sender_id']})</td>";
                echo "<td>{$msg['recipient_name']} ({$msg['recipient_id']})</td>";
                echo "<td>" . substr($msg['message'], 0, 50) . "...</td>";
                echo "<td>{$msg['timestamp']}</td>";
                echo "<td>" . ($msg['is_read'] ? 'Yes' : 'No') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    }
} else {
    echo "<div class='error'>❌ Error checking messages: " . mysqli_error($connection) . "</div>";
}
echo "</div>";

// Test 4: Check conversations
echo "<div class='section'>";
echo "<h3>4. Conversations Check</h3>";
$conv_query = "
    SELECT DISTINCT 
        CASE 
            WHEN sender_id = $user_id THEN recipient_id 
            ELSE sender_id 
        END as other_user_id,
        COUNT(*) as message_count,
        MAX(timestamp) as last_message_time
    FROM tbl_chat 
    WHERE sender_id = $user_id OR recipient_id = $user_id
    GROUP BY other_user_id
    ORDER BY last_message_time DESC
";

$conv_result = mysqli_query($connection, $conv_query);
if ($conv_result && mysqli_num_rows($conv_result) > 0) {
    echo "<h4>Your Conversations:</h4>";
    echo "<table>";
    echo "<tr><th>User ID</th><th>Name</th><th>Messages</th><th>Last Message</th></tr>";
    while ($conv = mysqli_fetch_assoc($conv_result)) {
        $user_info = mysqli_query($connection, "SELECT fname, lname FROM tbl_users WHERE id = {$conv['other_user_id']}");
        $user_data = mysqli_fetch_assoc($user_info);
        echo "<tr>";
        echo "<td>{$conv['other_user_id']}</td>";
        echo "<td>{$user_data['fname']} {$user_data['lname']}</td>";
        echo "<td>{$conv['message_count']}</td>";
        echo "<td>{$conv['last_message_time']}</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<div class='error'>❌ No conversations found for user $user_id</div>";
}
echo "</div>";

// Test 5: Test API endpoints
echo "<div class='section'>";
echo "<h3>5. API Endpoint Test</h3>";
echo "<div class='info'>Testing get_conversations API...</div>";

// Simulate API call
$_GET['action'] = 'get_conversations';
ob_start();
include 'chat_api.php';
$api_output = ob_get_clean();

echo "<details><summary>API Response</summary>";
echo "<pre>" . htmlspecialchars($api_output) . "</pre>";
echo "</details>";
echo "</div>";

// Test 6: JavaScript debugging
echo "<div class='section'>";
echo "<h3>6. JavaScript Debug Test</h3>";
echo "<button onclick='testChatAPI()'>Test Chat API</button>";
echo "<div id='api-result'></div>";

echo "<script>
async function testChatAPI() {
    const resultDiv = document.getElementById('api-result');
    resultDiv.innerHTML = 'Testing...';
    
    try {
        const response = await fetch('chat_api.php?action=get_conversations');
        const data = await response.json();
        resultDiv.innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
    } catch (error) {
        resultDiv.innerHTML = '<div class=\"error\">Error: ' + error.message + '</div>';
    }
}
</script>";
echo "</div>";

// Test 7: File permissions
echo "<div class='section'>";
echo "<h3>7. File Permissions Check</h3>";
$files_to_check = [
    'chat_api.php',
    'chat_realtime.php',
    'chatbox.js',
    '../db/dbconfig.php'
];

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        $perms = fileperms($file);
        echo "<div class='success'>✅ $file exists (permissions: " . substr(sprintf('%o', $perms), -4) . ")</div>";
    } else {
        echo "<div class='error'>❌ $file not found</div>";
    }
}
echo "</div>";

echo "<div class='section'>";
echo "<h3>8. Quick Fixes</h3>";
echo "<p>If you're seeing issues, try these:</p>";
echo "<ol>";
echo "<li><strong>Run the complete SQL schema:</strong> <a href='complete_chatbox_schema.sql' target='_blank'>Download SQL file</a></li>";
echo "<li><strong>Check database charset:</strong> Ensure your database uses utf8mb4</li>";
echo "<li><strong>Verify file paths:</strong> Make sure all chatbox files are in the correct directory</li>";
echo "<li><strong>Clear browser cache:</strong> Hard refresh (Ctrl+F5) to reload JavaScript</li>";
echo "</ol>";
echo "</div>";
?>
