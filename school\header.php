<?php
if(isset($_POST['logout_btn'])){
    session_destroy();
    $_SESSION = array();
    header('Location: ../index.php');
    exit();
}
    
if($_SESSION['new_pw'] != ""){
    header('Location: '.$uri.'/school/change_password.php');
    exit();
}
?><!DOCTYPE html>
<html lang="en">

<head>
    <!-- <meta charset="UTF-8"> -->
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bais City Division | PRIME HRM</title>

    <link rel="icon" type="image/x-icon" href="images/favicon.ico">
    <link rel="stylesheet" href="bootstrap-5.2.3-dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="css/signin.css">
    <link rel="stylesheet" href="../admin_panel/Poppins/css2.css">
    <link rel="stylesheet" href="font-awesome-4.7.0/css/font-awesome.min.css">
    <link rel="stylesheet" href="stylesheets/pace.css">
    <link rel="stylesheet" href="stylesheets/style.css">
    <link rel="stylesheet" href="stylesheets/cropper.css">
    <link rel="stylesheet" href="stylesheets/pdf_viewer.min.css">
    <link rel="stylesheet" href="textarea.css">
    <link rel="stylesheet" href="css/cookie.css">
    <!-- Add SweetAlert2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">

    <script type="text/javascript" src="bootstrap-5.2.3-dist/js/bootstrap.bundle.min.js"></script>
    <script type="text/javascript" src="javascripts/popper.min.js"></script>
    <script type="text/javascript" src="javascripts/bootstrap.js"></script>
    <script type="text/javascript" src="javascripts/adapter.min.js"></script>
    <script type="text/javascript" src="javascripts/vue.min.js"></script>
    <script type="text/javascript" src="javascripts/instascan.min.js"></script>
    <script type="text/javascript" src="javascripts/moment.min.js"></script>
    <script type="text/javascript" src="javascripts/cropper.js"></script>
    <script type="text/javascript" src="javascripts/pace.min.js"></script>
    <script type="text/javascript" src="javascripts/jquery-3.2.1.slim.min.js"></script>
    <script type="text/javascript" src="javascripts/jquery-3.6.4.min.js"></script>
    <script type="text/javascript" src="javascripts/jquery-3.5.1.min.js"></script>
    <script type="text/javascript" src="javascripts/pdf.worker.min.js"></script>
    <script type="text/javascript" defer src="javascripts/stacks.min.js"></script>
    <script type="text/javascript" defer src="javascripts/1.9.1-jquery.min.js"></script>
    <script type="text/javascript" defer src="javascripts/jquery-ui.min.js"></script>
    <!-- Add SweetAlert2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            font-weight: 300;
        }

        ​ p,
        ul,
        li {
            text-align: justify;
        }

        .jumbotron {
            position: relative;
            /* max-width: 800px; Maximum width */
            margin: 0 auto;
            /* Center it */
        }

        .jumbotron .content {
            position: absolute;
            /* Position the background text */
            bottom: 0;
            /* At the bottom. Use top:0 to append it to the top */
            background: rgb(0, 0, 0);
            /* Fallback color */
            background: rgba(0, 0, 0, 0.2);
            /* Black background with 0.5 opacity */
            color: #f1f1f1;
            /* Grey text */
            width: 100%;
            /* Full width */
            padding: 20px;
            /* Some padding */
        }

        img.shadw {
            -webkit-filter: drop-shadow(3px 3px 3px #222222);
            filter: drop-shadow(3px 3px 3px #222222);
        }

        .bg-primary,
        .bg-primary:active,
        .bg-primary:visited,
        .bg-primary:focus {
            background-color: #134693 !important;
            border-color: #134693 !important;
        }

        .bg-primary:hover {
            background-color: #2571E5 !important;
            border-color: #2571E5 !important;
        }

        a:hover {
            color: green;
        }

        .custom-tooltip {
            --bs-tooltip-bg: var(--bs-primary);
        }

        div.form-floating,
        select,
        option.opt,
        textarea,
        .drop button,
        .drop span {
            font-weight: 300;
        }

        #btn-top {
            position: fixed;
            bottom: 20px;
            right: 20px;
            border-radius: 10%;
            z-index: 1;
            display: none;
        }

        textarea.opt {
            font-weight: 300;
        }

        ::placeholder {
            font-weight: 300;
            opacity: 20%;
            color: #eee;
        }

        input[type=file] {
            font-weight: 300;
        }

        /* Profile Photo */
        .upload {
            width: 150px;
            position: relative;
            margin: auto;
        }

        .upload .round {
            position: absolute;
            bottom: 0;
            right: 15px;
            background: #00B4FF;
            width: 24px;
            height: 24px;
            line-height: 25px;
            text-align: center;
            font-size: 13px;
            border-radius: 50%;
            overflow: hidden;
        }

        .upload .round input[type="file"] {
            position: absolute;
            transform: scale(2);
            opacity: 0;
        }

        input[type=file]::-webkit-file-upload-button {
            cursor: pointer;
        }

        /* DROP RIGHT */
        .dropdown-menu li {
            position: relative;
        }

        .dropdown-menu .dropdown-submenu {
            display: none;
            position: absolute;
            left: 100%;
            top: -7px;
        }

        .dropdown-menu .dropdown-submenu-left {
            right: 100%;
            left: auto;
        }

        .dropdown-menu>li:hover>.dropdown-submenu {
            display: block;
        }

        .dropdown-hover:hover>.dropdown-menu {
            display: block;
        }

        .dropdown-hover>.dropdown-toggle:active {
            /*Without this, clicking will make it sticky*/
            pointer-events: none;
        }

        .divCenter {
            width: 70%;
        }

        @media (max-width: 600px) {

            /* Adjust styles for smaller screens */
            .divCenter {
                width: 100%;
            }
        }

        /* CHATBOX */

        #chat-box {
            display: none;
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 300px;
            max-height: 400px;
            border-radius: 15px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
            overflow-y: auto;
            background-color: #ffffff;
        }

        #chat-icon {
            position: fixed;
            bottom: 20px;
            right: 20px;
            cursor: pointer;
            background-color: #0084ff;
            color: #ffffff;
            border: none;
            border-radius: 50%;
            padding: 15px;
            font-size: 20px;
        }

        #message-input {
            width: calc(100% - 30px);
            border-radius: 5px;
        }

        .chat-message {
            background-color: #0084ff;
            color: #ffffff;
            border-radius: 10px;
            padding: 10px;
            margin-bottom: 10px;
            max-width: 80%;
        }

        #recipient-search {
            width: calc(100% - 30px);
            margin-bottom: 10px;
            border-radius: 5px;
        }

        .hidden-input {
            display: none;
        }


/* IMAGE img2 HIDE */
@media (max-width: 1120px) {
        #img2 {
            display: none;
        }
    }


    </style>

<!--  ERF form Select Script-->
<script>
    
        function showDiv(selectedValue) {
            // Hide all divs
            var divs = document.querySelectorAll('.option-div');
            divs.forEach(function (div) {
                div.style.display = 'none';
            });

            // Show the selected div
            var selectedDiv = document.getElementById(selectedValue);
            if (selectedDiv) {
                selectedDiv.style.display = 'block';
            }
        }

        // UPDATE action in tbl_erf
    $(document).ready(function(){
        $("#apply_button").click(function(){
            updateAction('apply');
        });

        $("#create_button").click(function(){
            updateAction('create');
        });

        function updateAction(action) {
            $.ajax({
                type: "POST",
                url: "erf_submit.php",
                data: { action: action },
                success: function(response){
                    alert(response); // Show response from the PHP script
                }
            });
        }
    });
    
    </script>

</head>

<body style="background-color:#f4f7fa;">
    <div id="cookie-message" class="cookie-message">
        Cookies accepted successfully!
    </div>

    <div id="cookie-popup" class="cookie-popup">
        <div class="cookie-content">
            <div class="cookie-text">
                This website uses cookies to ensure you get the best experience. By continuing to use this site, you consent to our use of cookies.
            </div>
            <button id="accept-cookies">Accept Cookies</button>
        </div>
    </div>

<?php include('leave_application.php');
// include('updatepds.php');
?>

    <button type="button" class="btn btn-light btn-floating shadow" id="btn-top">
        <i class="fa fa-chevron-up fa-sm"></i>
    </button>

    <script>
        //Get the button
        let mybutton = document.getElementById("btn-top");

        // When the user scrolls down 20px from the top of the document, show the button
        window.onscroll = function () {
            scrollFunction();
        };

        function scrollFunction() {
            if (
                document.body.scrollTop > 20 ||
                document.documentElement.scrollTop > 20
            ) {
                mybutton.style.display = "block";
            } else {
                mybutton.style.display = "none";
            }
        }
        // When the user clicks on the button, scroll to the top of the document
        mybutton.addEventListener("click", backToTop);

        function backToTop() {
            document.body.scrollTop = 0;
            document.documentElement.scrollTop = 0;
        }
    </script>
    <script>
        const userSession = '<?php echo isset($_SESSION['sess_id']) ? $_SESSION['sess_id'] : ''; ?>';
    </script>
    <script src="js/cookies.js"></script>
</body>
