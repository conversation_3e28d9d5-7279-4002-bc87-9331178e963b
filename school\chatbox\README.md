# Enhanced Chatbox System - Integration Guide

## Overview
This is a fully functional real-time chatbox system designed specifically for the `/school/` directory of your Prime HRM system. It features a right-to-left pullout design with comprehensive chat functionality.

## Features
- ✅ **Right-to-left pullout design** - Chat icon slides from right edge
- ✅ **Real-time messaging** - Instant message delivery using polling
- ✅ **User search** - Search bar to find users to chat with
- ✅ **File attachments** - Support for images, documents, PDFs (up to 10MB)
- ✅ **Message history** - Persistent conversation history
- ✅ **Notifications** - Desktop and in-app notifications with sound
- ✅ **Online status indicators** - Real-time user status (online/away/busy/offline)
- ✅ **Drag & drop** - Drag files directly into chat
- ✅ **Mobile responsive** - Works on all device sizes
- ✅ **Cross-browser compatible** - Works on modern browsers

## File Structure
```
school/
├── chatbox/
│   ├── chat_api.php           # Main API endpoints
│   ├── chat_realtime.php      # Real-time polling system
│   ├── chat_download.php      # File download handler
│   ├── chatbox.php           # Main UI components
│   ├── chatbox.js            # Core JavaScript functionality
│   ├── notification_handler.js # Enhanced notification system
│   └── uploads/              # File upload directory (auto-created)
├── chatbox_integration.php   # Integration script
└── profile.php              # Example integration
```

## Installation Steps

### 1. Database Setup
The enhanced database schema has been applied to your `hrdis` database with the following new tables:
- `tbl_conversations` - Manages chat conversations
- `tbl_chat_notifications` - Handles notifications
- `tbl_user_status` - Tracks online status
- `tbl_chat_files` - Manages file attachments
- `tbl_chat_settings` - User preferences

### 2. File Permissions
Ensure the upload directory has proper permissions:
```bash
chmod 755 school/chatbox/uploads/
```

### 3. Integration
To add the chatbox to any page in the `/school/` directory, simply include:
```php
<?php include 'chatbox_integration.php'; ?>
```

This has already been added to `profile.php` as an example.

## Usage Instructions

### For Users
1. **Opening Chat**: Click the blue chat icon on the right edge of the screen
2. **Finding Users**: Use the search bar to find colleagues to chat with
3. **Starting Conversations**: Click on any user from the search results or conversations list
4. **Sending Messages**: Type in the message box and press Enter or click Send
5. **File Sharing**: 
   - Click the paperclip icon to select files
   - Or drag and drop files directly into the chat area
   - Supported formats: Images (JPG, PNG, GIF), Documents (PDF, DOC, DOCX, XLS, XLSX), Text files
6. **Notifications**: Enable browser notifications when prompted for desktop alerts

### Chat Interface
- **Chat Panel**: Shows conversations and user search
- **Chat Window**: Active conversation view
- **Status Indicators**: 
  - 🟢 Online (green dot)
  - 🟡 Away (yellow dot) 
  - 🔴 Busy (red dot)
  - ⚫ Offline (gray dot)

## Technical Details

### Real-time Updates
- Uses long-polling technique for real-time message delivery
- Polls every 5 seconds for new messages
- Heartbeat every 30 seconds to maintain online status
- Auto-away after 15 minutes of inactivity (configurable)

### File Upload Limits
- Maximum file size: 10MB
- Allowed types: Images, PDFs, Word docs, Excel sheets, text files
- Files are stored in `school/chatbox/uploads/` with unique names
- Access control ensures users can only download files from their conversations

### Notifications
- Desktop notifications (requires user permission)
- In-app notification popups
- Sound alerts using Web Audio API
- Badge counters on chat icon and conversation list

### Security Features
- Session-based authentication
- File access control
- SQL injection prevention with prepared statements
- XSS protection with HTML escaping
- File type validation

## Browser Compatibility
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+
- Mobile browsers (iOS Safari, Chrome Mobile)

## Troubleshooting

### Common Issues

1. **Chat not loading**
   - Check if user is logged in (`$_SESSION['id']` exists)
   - Verify database connection
   - Check browser console for JavaScript errors

2. **Files not uploading**
   - Check upload directory permissions
   - Verify file size (max 10MB)
   - Ensure file type is supported

3. **Notifications not working**
   - User must grant notification permission
   - Check if browser supports notifications
   - Verify notification settings in chat

4. **Real-time updates not working**
   - Check if `chat_realtime.php` is accessible
   - Verify database connection
   - Check browser network tab for polling requests

### Debug Mode
To enable debug mode, add this to the top of `chat_api.php`:
```php
error_reporting(E_ALL);
ini_set('display_errors', 1);
```

## Customization

### Styling
The chatbox uses Bootstrap 5 classes and custom CSS. To customize:
1. Edit styles in `chatbox.php` or `chatbox_integration.php`
2. Modify colors by changing CSS variables
3. Adjust positioning and sizing as needed

### Settings
Users can customize:
- Sound notifications (on/off)
- Desktop notifications (on/off)
- Online status visibility (on/off)
- Auto-away timeout (5-60 minutes)
- Theme (light/dark - future feature)

### API Endpoints
Available endpoints in `chat_api.php`:
- `send_message` - Send a text message
- `get_messages` - Retrieve conversation messages
- `get_conversations` - Get user's conversation list
- `get_users` - Search for users
- `update_status` - Update online status
- `mark_as_read` - Mark messages as read
- `upload_file` - Upload file attachment
- `get_user_settings` - Get user preferences
- `update_settings` - Update user preferences

## Performance Optimization

### Database Indexes
The system includes optimized indexes for:
- Message queries by sender/recipient
- Conversation lookups
- User status updates
- File attachments

### Caching
Consider implementing:
- Redis for session storage
- Memcached for frequently accessed data
- CDN for file attachments

### Scaling
For high-traffic environments:
- Use WebSocket instead of polling
- Implement message queuing (Redis/RabbitMQ)
- Database read replicas
- Load balancing

## Support
For issues or questions:
1. Check the browser console for errors
2. Verify database connectivity
3. Test with different browsers
4. Check file permissions

The system is designed to be robust and handle various edge cases, but monitoring the error logs is recommended for production use.
