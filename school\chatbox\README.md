# 🚀 Modern Chatbox System

A complete, modern real-time chat system with emojis, file attachments, and typing indicators.

## ✨ Features

- **Real-time messaging** - Messages appear instantly without page refresh
- **Emoji support** - Full emoji picker with 100+ emojis
- **File attachments** - Support for images, documents, and other files
- **Online status** - See who's online, away, busy, or offline
- **Typing indicators** - Know when someone is typing
- **Mobile responsive** - Works perfectly on all devices
- **Modern UI** - Beautiful gradient design with smooth animations
- **Unread message counts** - Never miss a message
- **Search users** - Quickly find people to chat with
- **Conversation management** - Organized chat history

## 📋 Installation

### 1. Database Setup

Run the SQL schema to create the required tables:

```sql
-- Import the database schema
mysql -u username -p database_name < chatbox/database_schema.sql
```

Or manually run the SQL commands in `database_schema.sql`.

### 2. File Structure

Make sure your file structure looks like this:

```
your_project/
├── school/
│   └── chatbox/
│       ├── api.php                 # Main API endpoints
│       ├── chatbox.js             # JavaScript functionality
│       ├── chatbox.php            # Standalone chatbox page
│       ├── include_chatbox.php    # Include file for integration
│       ├── database_schema.sql    # Database tables
│       ├── demo.php              # Demo page
│       ├── README.md             # This file
│       └── uploads/              # File upload directory
│           └── chat/             # Chat file uploads
```

### 3. Permissions

Make sure the uploads directory is writable:

```bash
chmod 755 school/chatbox/uploads/
chmod 755 school/chatbox/uploads/chat/
```

### 4. Configuration

Update the database connection in `api.php` if needed:

```php
include '../db/dbconfig.php';
```

Make sure your session variables are set correctly. The system looks for:
- `$_SESSION['id']`
- `$_SESSION['user_id']`
- `$_SESSION['userid']`

## 🎯 Usage

### Quick Integration

Add this single line to any page where you want the chatbox:

```php
<?php include 'chatbox/include_chatbox.php'; ?>
```

### Standalone Page

Visit `chatbox/chatbox.php` for a standalone chat page.

### Demo

Visit `chatbox/demo.php` to see a complete demo with instructions.

## 🔧 API Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `api.php?action=get_conversations` | GET | Get user's conversations |
| `api.php?action=get_messages&recipient_id=X` | GET | Get messages for a conversation |
| `api.php?action=send_message` | POST | Send a new message |
| `api.php?action=upload_file` | POST | Upload and send a file |
| `api.php?action=get_users` | GET | Get list of users |
| `api.php?action=get_new_messages&last_message_id=X` | GET | Poll for new messages |
| `api.php?action=mark_read` | POST | Mark messages as read |
| `api.php?action=update_status` | POST | Update online status |
| `api.php?action=set_typing` | POST | Set typing indicator |

## 📱 How to Use

1. **Open Chat**: Click the chat button in the bottom-right corner
2. **Find Users**: Go to the "Users" tab to find people to chat with
3. **Start Conversation**: Click on a user to start chatting
4. **Send Messages**: Type and press Enter or click Send
5. **Add Emojis**: Click the emoji button to add emojis
6. **Attach Files**: Click the paperclip button to attach files
7. **View Status**: See online status indicators next to user names

## 🎨 Customization

### Colors

Update the gradient colors in the CSS:

```css
.chat-header, .chat-toggle {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
```

### Size

Change the chatbox dimensions:

```css
.chat-container {
    width: 350px;    /* Change width */
    height: 500px;   /* Change height */
}
```

### Position

Move the chatbox to a different corner:

```css
.chat-container, .chat-toggle {
    bottom: 20px;    /* Distance from bottom */
    right: 20px;     /* Distance from right */
    /* Or use left: 20px; for left side */
}
```

## 🔒 Security Features

- **SQL Injection Protection**: All queries use prepared statements
- **File Upload Validation**: File type and size validation
- **Session Management**: Proper session handling
- **XSS Protection**: HTML escaping for user content
- **CSRF Protection**: Session-based authentication

## 📊 Database Tables

- **tbl_chat_messages**: Stores all chat messages
- **tbl_chat_conversations**: Manages conversation metadata
- **tbl_chat_online_status**: Tracks user online status
- **tbl_chat_typing**: Manages typing indicators

## 🐛 Troubleshooting

### Messages not appearing in real-time

1. Check if JavaScript console shows any errors
2. Verify database connection in `api.php`
3. Make sure session variables are set correctly
4. Check file permissions for uploads directory

### File uploads not working

1. Check PHP upload settings in `php.ini`:
   ```ini
   upload_max_filesize = 10M
   post_max_size = 10M
   ```
2. Verify uploads directory permissions
3. Check if the uploads/chat directory exists

### Users not showing

1. Verify your `tbl_users` table structure
2. Check if the JOIN queries in `api.php` match your user table columns
3. Make sure users have proper session data

## 🚀 Performance Tips

- **Database Indexing**: The schema includes proper indexes for performance
- **File Size Limits**: Default 10MB limit for file uploads
- **Polling Frequency**: Default 2-second polling (adjustable in `chatbox.js`)
- **Message Limits**: Default 50 messages per conversation load

## 📄 License

This chatbox system is provided as-is for educational and commercial use.

## 🤝 Support

For issues or questions:
1. Check the troubleshooting section above
2. Review the demo page for examples
3. Check browser console for JavaScript errors
4. Verify database table structure matches the schema

---

**Enjoy your new modern chatbox! 🎉**
