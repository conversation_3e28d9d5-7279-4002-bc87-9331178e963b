<?php
session_start();
include '../db/dbconfig.php';

// Simple test script for chatbox functionality
// Access this file directly to test API endpoints

if (!isset($_SESSION['id'])) {
    echo "<h1>Chatbox Test - Please Login First</h1>";
    echo "<p>You need to be logged in to test the chatbox functionality.</p>";
    echo "<a href='../profile.php'>Go to Profile</a>";
    exit;
}

$user_id = $_SESSION['id'];
$user_name = $_SESSION['fname'] . ' ' . $_SESSION['lname'];

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chatbox Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
</head>
<body>
    <div class="container mt-5">
        <h1>Chatbox System Test</h1>
        <p>Welcome, <strong><?php echo htmlspecialchars($user_name); ?></strong> (ID: <?php echo $user_id; ?>)</p>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>API Tests</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-primary mb-2" onclick="testAPI('get_users')">Test Get Users</button><br>
                        <button class="btn btn-primary mb-2" onclick="testAPI('get_conversations')">Test Get Conversations</button><br>
                        <button class="btn btn-primary mb-2" onclick="testAPI('get_unread_count')">Test Unread Count</button><br>
                        <button class="btn btn-primary mb-2" onclick="testAPI('update_status')">Test Update Status</button><br>
                        <button class="btn btn-success mb-2" onclick="testNotification()">Test Notification</button><br>
                        <button class="btn btn-info mb-2" onclick="testRealtime()">Test Real-time Polling</button><br>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Test Results</h5>
                    </div>
                    <div class="card-body">
                        <pre id="test-results" style="max-height: 400px; overflow-y: auto; background: #f8f9fa; padding: 10px; border-radius: 4px;"></pre>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Database Status</h5>
                    </div>
                    <div class="card-body">
                        <?php
                        // Test database tables
                        $tables = [
                            'tbl_chat' => 'Chat messages',
                            'tbl_conversations' => 'Conversations',
                            'tbl_chat_notifications' => 'Notifications',
                            'tbl_user_status' => 'User status',
                            'tbl_chat_files' => 'File attachments',
                            'tbl_chat_settings' => 'User settings'
                        ];
                        
                        foreach ($tables as $table => $description) {
                            $result = mysqli_query($connection, "SHOW TABLES LIKE '$table'");
                            $exists = mysqli_num_rows($result) > 0;
                            $status = $exists ? 'success' : 'danger';
                            $icon = $exists ? 'check' : 'times';
                            
                            echo "<div class='alert alert-$status'>";
                            echo "<i class='fa fa-$icon'></i> $description ($table): ";
                            echo $exists ? 'EXISTS' : 'MISSING';
                            echo "</div>";
                        }
                        ?>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="alert alert-info">
                    <h6>Test Instructions:</h6>
                    <ul>
                        <li>Click the API test buttons to verify endpoints are working</li>
                        <li>Check that all database tables exist (green alerts above)</li>
                        <li>Test notifications to ensure browser permissions work</li>
                        <li>Open the actual chatbox by going to <a href="../profile.php">profile.php</a></li>
                        <li>Test with multiple users by opening different browser sessions</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function logResult(test, result) {
            const resultsEl = document.getElementById('test-results');
            const timestamp = new Date().toLocaleTimeString();
            resultsEl.textContent += `[${timestamp}] ${test}:\n${JSON.stringify(result, null, 2)}\n\n`;
            resultsEl.scrollTop = resultsEl.scrollHeight;
        }
        
        async function testAPI(endpoint) {
            try {
                let url = `chat_api.php?action=${endpoint}`;
                let options = { method: 'GET' };
                
                if (endpoint === 'update_status') {
                    url = 'chat_api.php?action=update_status';
                    options = {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ status: 'online' })
                    };
                }
                
                const response = await fetch(url, options);
                const data = await response.json();
                logResult(`API Test: ${endpoint}`, data);
            } catch (error) {
                logResult(`API Test: ${endpoint}`, { error: error.message });
            }
        }
        
        function testNotification() {
            if ('Notification' in window) {
                Notification.requestPermission().then(permission => {
                    if (permission === 'granted') {
                        new Notification('Chatbox Test', {
                            body: 'Notification system is working!',
                            icon: '../images/prime_hrm_logo.png'
                        });
                        logResult('Notification Test', { status: 'success', permission });
                    } else {
                        logResult('Notification Test', { status: 'denied', permission });
                    }
                });
            } else {
                logResult('Notification Test', { status: 'not_supported' });
            }
        }
        
        async function testRealtime() {
            try {
                const response = await fetch('chat_realtime.php?action=poll&last_check=2024-01-01 00:00:00');
                const data = await response.json();
                logResult('Real-time Polling Test', data);
            } catch (error) {
                logResult('Real-time Polling Test', { error: error.message });
            }
        }
        
        // Auto-run basic tests on page load
        window.addEventListener('load', function() {
            logResult('Page Load', { user_id: <?php echo $user_id; ?>, timestamp: new Date().toISOString() });
            
            // Test basic API endpoints
            setTimeout(() => testAPI('get_unread_count'), 1000);
            setTimeout(() => testAPI('get_users'), 2000);
        });
    </script>
</body>
</html>
