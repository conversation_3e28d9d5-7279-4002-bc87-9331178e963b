<?php
session_start();
include '../db/dbconfig.php';

// Check if user is logged in
if (!isset($_SESSION['id'])) {
    http_response_code(401);
    echo "Unauthorized";
    exit;
}

$user_id = $_SESSION['id'];
$file_id = $_GET['file_id'] ?? '';
$message_id = $_GET['message_id'] ?? '';

// Try to get file by message ID first (for backward compatibility)
if (!empty($message_id)) {
    $stmt = $connection->prepare("
        SELECT c.*, cf.original_name, cf.file_path, cf.file_size, cf.mime_type
        FROM tbl_chat c
        LEFT JOIN tbl_chat_files cf ON c.id = cf.message_id
        WHERE c.id = ? AND (c.sender_id = ? OR c.recipient_id = ?) AND c.message_type IN ('image', 'file')
    ");
    $stmt->bind_param("iii", $message_id, $user_id, $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $file = $result->fetch_assoc();

    if ($file) {
        $filePath = $file['file_path'];
        $fileName = $file['original_name'] ?: $file['file_name'];
        $mimeType = $file['mime_type'] ?: 'application/octet-stream';
    }
}
// Otherwise try to get file by file ID
else if (!empty($file_id)) {
    $stmt = $connection->prepare("
        SELECT cf.*, c.sender_id, c.recipient_id
        FROM tbl_chat_files cf
        JOIN tbl_chat c ON cf.message_id = c.id
        WHERE cf.id = ? AND (c.sender_id = ? OR c.recipient_id = ?)
    ");
    $stmt->bind_param("iii", $file_id, $user_id, $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $file = $result->fetch_assoc();

    if ($file) {
        $filePath = $file['file_path'];
        $fileName = $file['original_name'];
        $mimeType = $file['mime_type'];
    }
} else {
    http_response_code(400);
    echo "File ID or Message ID required";
    exit;
}

if (!$file) {
    http_response_code(404);
    echo "File not found or access denied";
    exit;
}

if (!file_exists($filePath)) {
    http_response_code(404);
    echo "File not found on server";
    exit;
}

// For images, show inline; for other files, force download
$isImage = strpos($mimeType, 'image/') === 0;
$disposition = $isImage ? 'inline' : 'attachment';

// Set headers for file display/download
header('Content-Type: ' . $mimeType);
header('Content-Disposition: ' . $disposition . '; filename="' . $fileName . '"');
header('Content-Length: ' . filesize($filePath));
header('Cache-Control: public, max-age=3600'); // Cache images for 1 hour
header('Expires: ' . gmdate('D, d M Y H:i:s', time() + 3600) . ' GMT');

// Output file
readfile($filePath);
exit;
?>
