<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Half Screen Chat Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #f8f9fa;
            font-family: Arial, sans-serif;
            height: 100vh;
        }
        
        .test-header {
            background: #28a745;
            color: white;
            padding: 20px;
            text-align: center;
            height: 50vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }
        
        .height-indicator {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.9);
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-size: 14px;
            z-index: 10000;
            border: 2px solid #ffc107;
        }
        
        .open-chat-btn {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 10000;
            padding: 20px 40px;
            font-size: 18px;
        }
        
        .instruction {
            background: #007bff;
            color: white;
            padding: 15px;
            margin: 20px;
            border-radius: 8px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="height-indicator" id="height-info">
        <div><strong>📱 Screen Info</strong></div>
        <div>Height: <span id="screen-height"></span>px</div>
        <div>Chat: <span id="chat-height">50vh</span></div>
        <div>Free: <span id="free-space"></span>px</div>
        <div style="margin-top: 10px; padding-top: 10px; border-top: 1px solid #666;">
            <div>Status: <span id="input-status" style="font-weight: bold;">Testing...</span></div>
        </div>
    </div>
    
    <button class="btn btn-primary btn-lg open-chat-btn" onclick="openChat()">
        <i class="fa fa-comments"></i><br>
        OPEN CHAT<br>
        <small>(Half Screen Test)</small>
    </button>
    
    <div class="test-header">
        <h1><i class="fa fa-mobile-alt"></i> Half Screen Chat Test</h1>
        <h3>50vh = 50% of Screen Height</h3>
        <p>This green area represents the TOP 50% of your screen</p>
        <p>Chat will open in the BOTTOM 50%</p>
        <p><strong>Input should be fully visible!</strong></p>
    </div>
    
    <div class="instruction">
        <h4>📋 Test Instructions</h4>
        <ol style="text-align: left; display: inline-block;">
            <li><strong>Click "OPEN CHAT" button</strong></li>
            <li><strong>Chat should open in bottom half only</strong></li>
            <li><strong>Look for blue border around input area</strong></li>
            <li><strong>Try typing in the input field</strong></li>
            <li><strong>Input should be fully visible</strong></li>
        </ol>
    </div>
    
    <!-- Include the chatbox -->
    <?php include 'chatbox.php'; ?>
    
    <!-- Include scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="chatbox.js"></script>
    
    <script>
        function updateHeightInfo() {
            const screenHeight = window.innerHeight;
            const chatHeight = Math.round(screenHeight * 0.5); // 50vh
            const freeSpace = screenHeight - chatHeight;
            
            document.getElementById('screen-height').textContent = screenHeight;
            document.getElementById('free-space').textContent = freeSpace;
            
            // Color code the free space
            const statusElement = document.getElementById('input-status');
            if (freeSpace >= 300) {
                statusElement.textContent = '✅ Excellent';
                statusElement.style.color = '#28a745';
            } else if (freeSpace >= 200) {
                statusElement.textContent = '✅ Good';
                statusElement.style.color = '#28a745';
            } else if (freeSpace >= 150) {
                statusElement.textContent = '⚠️ Okay';
                statusElement.style.color = '#ffc107';
            } else {
                statusElement.textContent = '❌ Tight';
                statusElement.style.color = '#dc3545';
            }
        }
        
        function openChat() {
            console.log('🔧 Opening half-screen chat...');
            const chatToggle = document.getElementById('chat-toggle');
            if (chatToggle) {
                chatToggle.click();
                
                // Check input visibility after chat opens
                setTimeout(() => {
                    checkInputVisibility();
                }, 1000);
            } else {
                alert('Chat toggle not found!');
            }
        }
        
        function checkInputVisibility() {
            const inputArea = document.querySelector('.chat-input-area, #message-input-area');
            const messageInput = document.getElementById('message-input');
            const chatPanel = document.getElementById('chat-panel');
            
            console.log('🔍 Checking input visibility...');
            
            if (chatPanel) {
                const chatRect = chatPanel.getBoundingClientRect();
                console.log('Chat panel:', {
                    top: chatRect.top,
                    bottom: chatRect.bottom,
                    height: chatRect.height,
                    screenHeight: window.innerHeight
                });
            }
            
            if (inputArea) {
                const inputRect = inputArea.getBoundingClientRect();
                const isFullyVisible = inputRect.bottom <= window.innerHeight && inputRect.top >= 0;
                
                console.log('Input area:', {
                    top: inputRect.top,
                    bottom: inputRect.bottom,
                    height: inputRect.height,
                    fullyVisible: isFullyVisible
                });
                
                if (isFullyVisible) {
                    console.log('✅ Input area is fully visible!');
                    alert('✅ SUCCESS: Input area is fully visible!\n\nTop: ' + Math.round(inputRect.top) + 'px\nBottom: ' + Math.round(inputRect.bottom) + 'px\nScreen: ' + window.innerHeight + 'px');
                } else {
                    console.log('❌ Input area is not fully visible');
                    alert('❌ ISSUE: Input area not fully visible\n\nInput bottom: ' + Math.round(inputRect.bottom) + 'px\nScreen height: ' + window.innerHeight + 'px\nOverflow: ' + Math.round(inputRect.bottom - window.innerHeight) + 'px');
                }
            } else {
                console.log('❌ Input area not found');
                alert('❌ ERROR: Input area not found');
            }
            
            if (messageInput) {
                messageInput.focus();
                console.log('✅ Input field focused');
            }
        }
        
        // Update on load and resize
        updateHeightInfo();
        window.addEventListener('resize', updateHeightInfo);
        window.addEventListener('orientationchange', () => {
            setTimeout(updateHeightInfo, 500);
        });
        
        console.log('📱 Half screen test loaded');
        console.log('Expected: Chat takes bottom 50% of screen');
        console.log('Expected: Input fully visible in chat area');
    </script>
</body>
</html>
