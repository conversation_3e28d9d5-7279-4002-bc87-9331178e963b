<nav class="navbar navbar-dark sticky-top navbar-expand-lg shadow" style="background-color: #04588c;">
    <div class="container-fluid">
        <a class="navbar-brand text-light" href="#">
            <img src="images/favicon.ico" alt="Logo" width="30" height="auto" class="d-inline-block align-text-top">
            PRIME-HRM</a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbar-togglerContent"
            aria-controls="navbar-togglerContent" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbar-togglerContent">
            <ul class="navbar-nav me-auto mb-2 mb-lg-0">
            <li class="nav-item">
                    <a class="nav-link active text-light" aria-current="page" href="about.php">About</a>
                </li>
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle text-light" aria-current="page" href="#" data-bs-toggle="dropdown"
                    aria-expanded="false">
                        Profile
                    </a>
                    <ul class="dropdown-menu">
                        <li><a style="font-weight: 300;" class="item dropdown-item" href="profile.php">Page 1</a></li>
                    </ul>
                </li>
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle text-light" href="#" role="button" data-bs-toggle="dropdown"
                        aria-expanded="false">
                        Pillars
                    </a>
                    <ul class="dropdown-menu">
                        <li><a style="font-weight: 300;" class="item dropdown-item" href="#">Recruitment, Selection, &
                                Placement</a></li>
                        <li><a style="font-weight: 300;" class="item dropdown-item" href="#">Learning & Development</a>
                        </li>
                        <li><a style="font-weight: 300;" class="item dropdown-item" href="#">Performance Management</a>
                        </li>
                        <li><a style="font-weight: 300;" class="item dropdown-item" href="#">Rewards & Recognition</a>
                        </li>
                        <!-- <li><hr class="dropdown-divider"></li> -->
                    </ul>
                </li>
                <li class="nav-item dropdown">
                    <a class="drop nav-link dropdown-toggle text-light" href="#" role="button" data-bs-toggle="dropdown"
                        aria-expanded="false">
                        Services
                    </a>
                    <ul class="dropdown-menu">
                    <li><span style="font-weight: 300;" data-bs-toggle="modal" data-bs-target="#serviceRecordModal"
                    type="button" class="drop dropdown-item">Service Record Request</span></li>
                        <li><span style="font-weight: 300;" data-bs-toggle="modal" data-bs-target="#leaveAppModal"
                                type="button" class="drop dropdown-item">Application for Leave</span></li>
                        <li>
                            <hr class="dropdown-divider">
                        </li>
                        <li><a style="font-weight: 300;" class="dropdown-item" href="erf_apply.php" target="_self">ERF
                                or Reclassification</a></li>
                    </ul>
                </li>
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle text-light" href="#" role="button" data-bs-toggle="dropdown"
                        aria-expanded="false">
                        Downloads
                    </a>
                    <ul class="dropdown-menu">
                        <!-- <li><a style="font-weight: 300;" class="down dropdown-item" href="#">CS Form 212 (PDS)</a></li> -->
                        <li><a href="file/Equivalent_Review_Form.doc" target="_blank" style="font-weight: 300;"
                                type="button" class="drop dropdown-item" id="navbarDropdownMenuLink">
                                Equivalent Record Form (ERF) <i class="text-primary fa fa-file-word-o"
                                    aria-hidden="true"></i></a></li>
                        <!-- SUB-MENU -->
                        <li>
                            <span style="font-weight: 300;" class="dropdown-item">
                                CS Form 212 (PDS) <i style="font-weight: 300;" class="float-end fa fa-caret-right"
                                    aria-hidden="true"></i>
                            </span>
                            <ul class="dropdown-menu dropdown-submenu" aria-labelledby="navbarDropdownMenuLink">
                                <?php
                                    $q = "SELECT * FROM tbl_c4 WHERE empnum='".$_SESSION['empnum']."' ";
                                    $qr = mysqli_query($connection, $q);
                                    while($row=mysqli_fetch_array($qr)){
                                        if(!empty($row['reference1'] AND $row['reference2'] AND $row['reference3'])){
                                ?>
                                <li>
                                    <a href="downloadpds.php" target="_BLANK" style="font-weight: 300;"
                                        class="dropdown-item" type="button">Download</a>
                                </li>
                                <?php }; }; ?>
                                <li>
                                    <a href="updatepds.php" target="_SELF" style="font-weight: 300;"
                                        class="dropdown-item" type="button" data-bs-toggle="modal" data-bs-target="#myModal">Update CS Form 212 - C4</a>
                                </li>
                            </ul>
                        </li>

                    </ul>
                </li>
            </ul>
            <ul class="navbar-nav">
            <?php
                include "db/dbconfig.php";
                $sql = "SELECT COUNT(*) as count FROM tbl_log WHERE empnum = ? AND is_read = 0";
                if ($stmt = $connection->prepare($sql)) {
                    $stmt->bind_param("s", $_SESSION['empnum']);
                    $stmt->execute();
                    $result = $stmt->get_result();
                    $row = $result->fetch_assoc();
                    $count = $row['count'];
            
                    if($count == 0){
                        echo '
                        <li class="nav-item">
                            <span class="text-light nav-link nav-user">
                                <i class="fa fa-bell fa-lg" aria-hidden="true"></i>
                            </span>
                        </li>';
                    } else {
                        echo '
                        <li class="nav-item">
                            <div class="position-relative d-inline-block">
                                <span class="text-light nav-link nav-user notification-bell" style="cursor: pointer;" data-bs-toggle="offcanvas" 
                                    data-bs-target="#notify" aria-controls="offcanvasRight">
                                    <i class="fa fa-bell fa-lg" aria-hidden="true"></i>
                                <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger notification-badge" 
                                    style="font-size: 0.75rem; margin-top: 10px; margin-left: -10px;">
                                    '.$count.'
                                </span>
                                </span>
                            </div>
                        </li>';
                    }
                    $stmt->close();
                }
            ?>
            
            <script>
            $(document).ready(function() {
                console.log('jQuery is loaded in nav.php');
                $('.notification-bell').click(function() {
                    console.log('Notification bell clicked in nav.php');
                    $.ajax({
                        url: '/prime-hrm/school/mark_notifications_read.php',  // Updated path
                        type: 'POST',
                        data: { mark_read: true },
                        success: function(response) {
                            console.log('Response:', response);
                            if(response.trim() === 'success') {
                                $('.notification-badge').fadeOut();
                            } else {
                                console.log('Error:', response);
                            }
                        },
                        error: function(xhr, status, error) {
                            console.log('Ajax error:', error);
                        }
                    });
                });
            });
            </script>
            
                <li class="nav-item"><a class="text-light nav-user nav-link" href="#" style="cursor:default;">
                        <?php echo "Welcome ". $_SESSION['fname']; ?></a>
                </li>
                <li class="nav-item dropdown center">
                    <a class="text-light nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown"
                        aria-expanded="false">
                        <?php
                    $qry = "SELECT * FROM tbl_users WHERE empnum = '".$_SESSION['empnum']."'";
                    $q_r = mysqli_query($connection,$qry);
                    foreach($q_r as $r){
                    ?>
                        <?= (($r['image'] == '') ? '<img src="images/profile/thumbs/profile.png" width="30px" alt="">':
                        '<img src="images/profile/thumbs/'.$r['image'].'" class="rounded-circle" width="30px" alt="">');
                        }?>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-lg-end">
                        <?php
                            if(in_array($_SESSION['usertype'], [1, 4, 5, 6, 7, 9, 12, 14])){
                            echo '                        <li>
                            <a href="../admin_panel/admin_panel.php" target="_self" class="drop btn dropdown-item"
                                type="submit" name="profile_btn"><span class="small"><i class="fa fa-dashboard"
                                        aria-hidden="true"></i> Admin Page</span></a>
                            </li>
                            ';
                            };
                            if($_SESSION['usertype'] == 3){
                                echo '                        <li>
                                <a href="../admin_panel/admin_panel.php" target="_self" class="drop btn dropdown-item"
                                    type="submit" name="profile_btn"><span class="small"><i class="fa fa-users"
                                            aria-hidden="true"></i> Human Resource</span></a>
                                </li>
                                ';
                                };
                                if(in_array($_SESSION['usertype'], [8, 11])){
                                    echo '                        <li>
                                    <a href="../admin_panel/admin_panel.php" target="_self" class="drop btn dropdown-item"
                                        type="submit" name="profile_btn"><span class="small"><i class="fa fa-users"
                                                aria-hidden="true"></i>'.(($_SESSION['usertype'] == 11)? ' Admin Tasks Panel' : ' HR Tasks Panel').'</span></a>
                                    </li>
                                    ';
                                    };
    
                                ?>
                        <?php //if(in_array($_SESSION['usertype'], [0])){ ?>
                        <li>
                            <a class="drop btn dropdown-item" href="dashboard/me.php"><span class="small">
                                <i class="fa fa-list-ul" aria-hidden="true"></i> Transactions</span>
                            </a>
                        </li>
                        <?php //} ?>
                        <li>
                            <button class="drop btn dropdown-item" data-bs-toggle="modal"
                                data-bs-target="#cpassModal<?php echo $_SESSION['empnum']; ?>"><span class="small">
                                <i class="fa fa-lock" aria-hidden="true"></i> Update Password</span>
                            </button>
                        </li>
                        <li>
                            <button class="drop btn dropdown-item" data-bs-toggle="modal"
                                data-bs-target="#developmentalPlanModal<?php echo $_SESSION['empnum']; ?>"><span class="small">
                                <i class="fa fa-edit" aria-hidden="true"></i> Development Plan</span>
                            </button>
                        </li>
                        <?php 
                        if(in_array($_SESSION['usertype'], [1, 17]) || $_SESSION['empnum'] == 4690834) { 
                        ?>
                        <li>
                            <a href="sppd.php" class="drop btn dropdown-item"><span class="small">
                                <i class="fa fa-file-text" aria-hidden="true"></i> SPPD</span>
                            </a>
                        </li>
                        <?php } ?>
                        <li>
                            <hr class="dropdown-divider">
                        </li>
                        <li>
                                <button class="drop btn dropdown-item" data-bs-toggle="modal" data-bs-target="#esignModal"><span
                                        class="small"><i class="fa fa-fire" aria-hidden="true"></i>
                                        <?php
                                        $empnum = $_SESSION['empnum'];
                                        $esign="SELECT * FROM tbl_esign WHERE `empnum` = ? ";
                                        $stmt = mysqli_prepare($connection, $esign);

                                        if ($stmt) {
                                        mysqli_stmt_bind_param($stmt, "i", $empnum);
                                        mysqli_stmt_execute($stmt);
                                        $result = mysqli_stmt_get_result($stmt);
                                        
                                        if(mysqli_num_rows($result) > 0) {
                                            echo "Update E-sign";
                                        }else{
                                            echo "Upload E-sign";
                                        } } ?>
                                        
                                    </span></button>
                        </li>
                        <li>
                            <hr class="dropdown-divider">
                        </li>
                        <li>
                            <form method="POST" action="">
                                <input type="hidden" name="empnum" value="<?php echo $_SESSION['empnum']; ?>">
                                <button class="drop btn dropdown-item" type="submit" name="logout_btn"><span
                                        class="small"><i class="fa fa-sign-out" aria-hidden="true"></i>
                                        Logout</span></button>
                            </form>
                        </li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>
</nav>

<?php
include('dev_planModal.php');
include('c4_modal.php');
include('../admin_panel/activity_log.php');
include('notif.php');
// include('search_modal.php');
?>

<!-- Search Modal -->
<div class="modal" id="searchModal" tabindex="-1" aria-labelledby="searchModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="searchModalLabel">Search by Track ID</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <form id="searchForm" action="search.php" method="POST">
          <div class="mb-3">
            <label for="trackId" class="form-label">Track ID:</label>
            <input type="text" class="form-control" id="trackId" name="trackId" required>
          </div>
          <button type="submit" class="btn btn-primary">Search</button>
        </form>
      </div>
    </div>
  </div>
</div>

<!-- Change Pass Modal -->
<div class="modal" id="cpassModal<?php echo $_SESSION['empnum']; ?>" tabindex="-1"
    aria-labelledby="cpassModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="cpassModalLabel"><i class="fa fa-lock" aria-hidden="true"></i> Update Password</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
            <form action="authController.php" method="POST">
                <div class="mb-3">
                    <div class="form-floating mb-3 password-input-wrapper">
                        <input type="password" 
                               class="form-control" 
                               id="rpword" 
                               placeholder="Password"
                               name="new_pass" 
                               pattern="^(?=.*[A-Za-z])(?=.*\d)(?=.*[@#$!+\-_/])[A-Za-z\d@#$!+\-_/]{8,}$"
                               oninput="checkPassword(this)"
                               required>
                        <i class="text-muted fa fa-eye-slash eye-icon" id="toggleRPassword"></i>
                        <label for="rpword">New Password</label>
                        <div id="passFeedback" class="password-require" style="font-size: 0.875em;"></div>
                    </div>
                </div>
                <div class="mb-3">
                    <div class="form-floating mb-3 password-input-wrapper">
                        <input type="password" 
                               class="form-control" 
                               id="rnpword"
                               placeholder="Confirm Password" 
                               name="rnew_pass" 
                               oninput="checkConfirmPassword(this)"
                               required>
                        <i class="text-muted fa fa-eye-slash eye-icon" id="toggleRNPassword"></i>
                        <label for="rnpword">Repeat New Password</label>
                        <div id="confirmPassFeedback" class="password-require" style="font-size: 0.875em;"></div>
                    </div>
                </div>
                    <button type="submit" class="float-end btn btn-outline-success" id="newpass_btn_update" name="newpass_profile2" disabled>Update</button>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
.password-require {
    color: #6c757d;
}
.require-met {
    color: #198754;
}
.require-unmet {
    color: #dc3545;
}
/* Add these styles for the eye icon positioning */
.password-input-wrapper {
    position: relative;
}
.eye-icon {
    position: absolute;
    right: 10px;
    top: 20px;  /* Fixed pixel value instead of percentage */
    transform: none;  /* Remove transform since we're using fixed positioning */
    cursor: pointer;
    z-index: 10;
}
.form-floating > .form-control {
    padding-right: 35px; /* Add space for the eye icon */
}
</style>
<script>
function checkPassword(input) {
const password = input.value;
const requirements = {
    length: password.length >= 8,
    letter: /[A-Za-z]/.test(password),
    number: /\d/.test(password),
    special: /[@#$!+\-_/]/.test(password)
};

// Add this: Check if all requirements are met
const allRequirementsMet = Object.values(requirements).every(req => req === true);
const confirmPassword = document.getElementById('rnpword').value;
const updateButton = document.getElementById('newpass_btn_update');

// Enable button only if all requirements are met AND passwords match
updateButton.disabled = !(allRequirementsMet && password === confirmPassword);

const feedback = document.getElementById('passFeedback');
feedback.innerHTML = `
    <div class="${requirements.length ? 'requirement-met' : 'requirement-unmet'}">
        ${requirements.length ? '<i class="fa fa-check-circle text-success" aria-hidden="true"></i>' : '<i class="fa fa-times-circle text-danger" aria-hidden="true"></i>'} Minimum 8 characters
    </div>
    <div class="${requirements.letter ? 'requirement-met' : 'requirement-unmet'}">
        ${requirements.letter ? '<i class="fa fa-check-circle text-success" aria-hidden="true"></i>' : '<i class="fa fa-times-circle text-danger" aria-hidden="true"></i>'} At least one letter
    </div>
    <div class="${requirements.number ? 'requirement-met' : 'requirement-unmet'}">
        ${requirements.number ? '<i class="fa fa-check-circle text-success" aria-hidden="true"></i>' : '<i class="fa fa-times-circle text-danger" aria-hidden="true"></i>'} At least one number
    </div>
    <div class="${requirements.special ? 'requirement-met' : 'requirement-unmet'}">
        ${requirements.special ? '<i class="fa fa-check-circle text-success" aria-hidden="true"></i>' : '<i class="fa fa-times-circle text-danger" aria-hidden="true"></i>'} At least one special character (/@#$!+-_)
    </div>
`;

checkConfirmPassword(document.getElementById('rnpword'));
}

function checkConfirmPassword(input) {
const password = document.getElementById('rpword').value;
const confirmPassword = input.value;
const feedback = document.getElementById('confirmPassFeedback');
const updateButton = document.getElementById('newpass_btn_update');

// Check all password requirements
const requirements = {
    length: password.length >= 8,
    letter: /[A-Za-z]/.test(password),
    number: /\d/.test(password),
    special: /[@#$!+\-_/]/.test(password)
};
const allRequirementsMet = Object.values(requirements).every(req => req === true);

// Enable button only if all requirements are met AND passwords match
updateButton.disabled = !(allRequirementsMet && password === confirmPassword);

if (confirmPassword) {
    if (password === confirmPassword) {
        feedback.innerHTML = '<div class="requirement-met"><i class="fa fa-check-circle text-success" aria-hidden="true"></i> Passwords match</div>';
        input.setCustomValidity('');
    } else {
        feedback.innerHTML = '<div class="requirement-unmet"><i class="fa fa-times-circle text-danger" aria-hidden="true"></i> Passwords do not match</div>';
        input.setCustomValidity('Passwords do not match');
    }
} else {
    feedback.innerHTML = '';
    input.setCustomValidity('');
}
}
// Toggle password visibility
document.getElementById('toggleRPassword').addEventListener('click', function () {
    const passwordInput = document.getElementById('rpword');
    const type = passwordInput.getAttribute('type') === 'password' ?
        'text' : 'password';
    passwordInput.setAttribute('type', type);
    this.classList.toggle('fa-eye');
    this.classList.toggle('fa-eye-slash');
});

document.getElementById('toggleRNPassword').addEventListener('click', function () {
    const passwordInput = document.getElementById('rnpword');
    const type = passwordInput.getAttribute('type') === 'password' ?
        'text' : 'password';
    passwordInput.setAttribute('type', type);
    this.classList.toggle('fa-eye');
    this.classList.toggle('fa-eye-slash');
});
</script>

<!-- Result Modal -->
<div class="modal fade" id="resultModal" tabindex="-1" aria-labelledby="resultModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="resultModalLabel">Search Results</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body" id="resultModalBody">
        <!-- Results will be displayed here -->
      </div>
    </div>
  </div>
</div>

<!-- E-SIGNATURE Modal -->
<div class="modal" id="esignModal" tabindex="-1" aria-labelledby="esignModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="esignModalLabel">
        <?php
                                        $empnum = $_SESSION['empnum'];
                                        $esign="SELECT * FROM tbl_esign WHERE `empnum` = ? ";
                                        $stmt = mysqli_prepare($connection, $esign);

                                        if ($stmt) {
                                        mysqli_stmt_bind_param($stmt, "i", $empnum);
                                        mysqli_stmt_execute($stmt);
                                        $result = mysqli_stmt_get_result($stmt);
                                        
                                        if(mysqli_num_rows($result) > 0) {
                                            echo "Update E-signature";
                                        }else{
                                            echo "Upload E-signature";
                                        } } ?>
        </h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body" id="esignModalBody">
            <input type="file" name='crop_esign' class='pointer crop_esign file form-control' id='upload_esign'>
      </div>
    </div>
  </div>
</div>

<script>
    // Submit the form using AJAX
    document.addEventListener('DOMContentLoaded', function () {
        const searchForm = document.getElementById('searchForm');
        const resultModalBody = document.getElementById('resultModalBody');
        const searchModal = new bootstrap.Modal(document.getElementById('searchModal'));

        searchForm.addEventListener('submit', function (e) {
            e.preventDefault();

            const formData = new FormData(searchForm);

            fetch('search.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                // Display search results in the result modal body
                displayResults(data);
            })
            .catch(error => {
                console.error('Error:', error);
            });
        });

        // Display search results in the result modal body
        function displayResults(results) {
            resultModalBody.innerHTML = '';

            if (results.length > 0) {
                results.forEach(result => {
                    // Explode the result based on the symbol "-"
                    const resultArray = result.split('-');

                    // Customize the display and echo a personalized message
                    resultModalBody.innerHTML += 'Track ID: ' + resultArray[1] + '<br>';
                    resultModalBody.innerHTML += 'Message: ' + getPersonalizedMessage(resultArray[1]) + '<br>';
                });
            } else {
                resultModalBody.innerHTML = 'No results found.';
            }

            // Show the result modal
            $('#resultModal').modal('show');

            // Close the search modal
            searchModal.hide();
        }

        // Function to generate a personalized message based on the second part of the result
        function getPersonalizedMessage(messagePart) {
            // Customize the messages based on your requirements
            if (messagePart === '2') {
                return 'This is message A.';
            } else if (messagePart === 'B') {
                return 'This is message B.';
            } else {
                return 'Unknown message.';
            }
        }
    });
</script>

<!-- CROP E-SIGNATURE -->
<script>
    $(document).ready(function () {
        var $modal = $('#modal_esign');
        var crop_esign = document.getElementById('sample_image');
        var cropper;
        $('#upload_esign').change(function (event) {
            var files = event.target.files;
            var done = function (url) {
                crop_esign.src = url;
                $modal.modal('show');
            };
            if (files && files.length > 0) {
                reader = new FileReader();
                reader.onload = function (event) {
                    done(reader.result);
                };
                reader.readAsDataURL(files[0]);
            }
        });

        $modal.on('shown.bs.modal', function () {
        cropper = new Cropper(crop_esign, {
            aspectRatio: NaN, // Allows freeform cropping
            viewMode: 2,
            minContainerWidth: 200,
            minContainerHeight: 200,
            minCropBoxWidth: 50,
            minCropBoxHeight: 50,
            autoCropArea: 0.8,
            responsive: true,
            preview: '.preview'
        });

        }).on('hidden.bs.modal', function () {
            cropper.destroy();
            cropper = null;
        });

        $('#crop_and_upload_esign').click(function () {
            canvas = cropper.getCroppedCanvas({
                width: 400,
                height: 400
            });
            canvas.toBlob(function (blob) {
                url = URL.createObjectURL(blob);
                var reader = new FileReader();
                reader.readAsDataURL(blob);
                reader.onloadend = function () {
                    var base64data = reader.result;
                    $.ajax({
                        url: 'e_signController.php',
                        method: 'POST',
                        data: {
                            crop_esign: base64data
                        },
                        success: function (data) {
                            $modal.modal('hide');
                            location.reload();
                            alert(
                                "You have succesfully uploaded your e-signature!"
                            );
                        }
                    });
                };
            });
        });
    });
</script>


<!-- CROP E-SIGNATURE MODAL -->
<div class="modal fade" data-backdrop="static" id="modal_esign" tabindex="-1" role="dialog" aria-labelledby="modalLabel"
    aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">E-signature</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="img-container">
                    <div class="row">
                        <div class="col-md-8">
                            <img class="img-fluid" src="" id="sample_image" />
                        </div>
                        <div class="col-md-4">
                            <div class="preview"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <span id="crop_and_upload_esign" class="btn btn-primary">Upload</span>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
            </div>
        </div>
    </div>
</div>
<!-- END OF CROP E-SIGNATURE MODAL -->

<script>
// Ensure proper dropdown behavior - close other dropdowns when one is opened
document.addEventListener('DOMContentLoaded', function() {
    // Get all dropdown toggles
    const dropdownToggles = document.querySelectorAll('[data-bs-toggle="dropdown"]');

    dropdownToggles.forEach(function(toggle) {
        toggle.addEventListener('click', function(e) {
            // Close all other open dropdowns
            dropdownToggles.forEach(function(otherToggle) {
                if (otherToggle !== toggle) {
                    const otherDropdown = bootstrap.Dropdown.getInstance(otherToggle);
                    if (otherDropdown) {
                        otherDropdown.hide();
                    }
                }
            });
        });
    });

    // Also close dropdowns when clicking outside
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.dropdown')) {
            dropdownToggles.forEach(function(toggle) {
                const dropdown = bootstrap.Dropdown.getInstance(toggle);
                if (dropdown) {
                    dropdown.hide();
                }
            });
        }
    });
});
</script>

<?php include 'service_recordModal.php'; ?>
