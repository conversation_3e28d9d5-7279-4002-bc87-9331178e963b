<?php
/**
 * Include this file in any page where you want to show the chatbox
 * Usage: include 'chatbox/include_chatbox.php';
 */

// session_start();
$current_user_id = $_SESSION['id'] ?? $_SESSION['user_id'] ?? $_SESSION['userid'] ?? null;

if (!$current_user_id) {
    // Don't show chatbox if user is not logged in
    return;
}
?>

<!-- Modern Chatbox Styles -->
<style>
/* Chatbox CSS Reset - Prevent conflicts with existing styles */
#chatContainer,
#chatContainer *,
#chatToggle {
    box-sizing: border-box;
}

/* Ensure dropdowns and other Bootstrap components work normally */
.dropdown-toggle,
.nav-link,
.navbar-nav {
    position: relative !important;
    z-index: auto !important;
}

/* Allow dropdown menus to position properly */
.dropdown-menu {
    z-index: 1050 !important;
}

/* Modern Chat Styles - Scoped to prevent conflicts */
#chatContainer.chat-container {
    position: fixed;
    bottom: 70px;
    right: 20px;
    width: 350px;
    height: 500px;
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    display: none;
    flex-direction: column;
    z-index: 999;
    overflow: hidden;
}

#chatContainer.chat-container.open {
    display: flex;
}

#chatContainer .chat-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px;
    border-radius: 15px 15px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

#chatToggle.chat-toggle {
    position: fixed;
    bottom: 70px;
    right: 20px;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 50%;
    color: white;
    font-size: 24px;
    cursor: pointer;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    z-index: 998;
    transition: all 0.3s ease;
}

#chatToggle.chat-toggle:hover {
    transform: scale(1.1);
}

#chatContainer .chat-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

#chatContainer .chat-tabs {
    display: flex;
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

#chatContainer .chat-tab {
    flex: 1;
    padding: 10px;
    text-align: center;
    cursor: pointer;
    border: none;
    background: none;
    transition: all 0.3s ease;
}

#chatContainer .chat-tab.active {
    background: white;
    border-bottom: 2px solid #667eea;
    color: #667eea;
}

#chatContainer .chat-content {
    flex: 1;
    overflow: hidden;
    position: relative;
}

#chatContainer .tab-content {
    display: none;
    height: 100%;
    flex-direction: column;
}

#chatContainer .tab-content.active {
    display: flex;
}

#chatContainer .conversations-list,
#chatContainer .users-list {
    flex: 1;
    overflow-y: auto;
    padding: 8px 12px;
    max-height: 350px;
}

#chatContainer .users-list {
    background: #fafafa;
}

#chatContainer .conversation-item,
#chatContainer .user-item {
    display: flex;
    align-items: center;
    padding: 12px;
    border-radius: 8px;
    cursor: pointer;
    margin-bottom: 8px;
    transition: all 0.3s ease;
    border: 1px solid transparent;
}

#chatContainer .conversation-item:hover,
#chatContainer .user-item:hover {
    background: #f8f9fa;
    border-color: #e9ecef;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

#chatContainer .conversation-item.active {
    background: #e3f2fd;
}

#chatContainer .user-avatar {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    margin-right: 12px;
    object-fit: cover;
    flex-shrink: 0;
    border: 2px solid #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

#chatContainer .user-info {
    flex: 1;
    min-width: 0;
    overflow: hidden;
}

#chatContainer .user-name {
    font-weight: 600;
    margin-bottom: 3px;
    font-size: 14px;
    color: #333;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

#chatContainer .last-message {
    font-size: 12px;
    color: #6c757d;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.3;
}

.unread-badge {
    background: #dc3545;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
}

.online-status {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    position: absolute;
    bottom: 2px;
    right: 2px;
    border: 2px solid white;
}

.online-status.online {
    background: #28a745;
}

.online-status.away {
    background: #ffc107;
}

.online-status.busy {
    background: #dc3545;
}

.online-status.offline {
    background: #6c757d;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 15px;
    background: #f8f9fa;
}

.message {
    margin-bottom: 15px;
    display: flex;
    align-items: flex-end;
}

.message.own {
    flex-direction: row-reverse;
}

.message-content {
    max-width: 70%;
    padding: 10px 15px;
    border-radius: 18px;
    position: relative;
}

.message.own .message-content {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.message:not(.own) .message-content {
    background: white;
    border: 1px solid #e9ecef;
}

.message-time {
    font-size: 10px;
    opacity: 0.7;
    margin-top: 5px;
}

.chat-input-area {
    padding: 15px;
    background: white;
    border-top: 1px solid #dee2e6;
}

.input-group {
    position: relative;
}

.message-input {
    border-radius: 25px;
    border: 1px solid #dee2e6;
    padding: 10px 50px 10px 15px;
    resize: none;
    max-height: 100px;
}

.input-actions {
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    gap: 5px;
}

.input-btn {
    width: 35px;
    height: 35px;
    border: none;
    border-radius: 50%;
    background: #667eea;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.input-btn:hover {
    background: #5a6fd8;
    transform: scale(1.1);
}

.emoji-picker {
    position: absolute;
    bottom: 60px;
    right: 0;
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 10px;
    padding: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    display: none;
    max-height: 200px;
    overflow-y: auto;
    width: 250px;
}

.emoji-grid {
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    gap: 5px;
}

.emoji-item {
    padding: 5px;
    cursor: pointer;
    border-radius: 5px;
    text-align: center;
    font-size: 18px;
}

.emoji-item:hover {
    background: #f8f9fa;
}

.typing-indicator {
    padding: 10px 15px;
    font-style: italic;
    color: #6c757d;
    font-size: 12px;
}

.file-preview {
    max-width: 200px;
    border-radius: 10px;
    margin-top: 5px;
}

@media (max-width: 768px) {
    .chat-container {
        width: 100%;
        height: 100%;
        bottom: 0;
        right: 0;
        border-radius: 0;
    }

    .chat-toggle {
        bottom: 70px;
        right: 10px;
    }
}
</style>

<!-- Chat Toggle Button -->
<button class="chat-toggle" id="chatToggle">
    <i class="fa fa-comments"></i>
</button>

<!-- Chat Container -->
<div class="chat-container" id="chatContainer">
    <!-- Chat Header -->
    <div class="chat-header">
        <h6 class="mb-0">
            <i class="fa fa-comments me-2"></i>
            <span id="chatTitle">Messages</span>
        </h6>
        <button class="btn btn-sm text-white" id="closeChat">
            <i class="fa fa-times"></i>
        </button>
    </div>

    <!-- Chat Body -->
    <div class="chat-body">
        <!-- Chat Tabs -->
        <div class="chat-tabs">
            <button class="chat-tab active" data-tab="conversations">
                <i class="fa fa-comments me-1"></i>Chats
            </button>
            <button class="chat-tab" data-tab="users">
                <i class="fa fa-users me-1"></i>Users
            </button>
        </div>

        <!-- Chat Content -->
        <div class="chat-content">
            <!-- Conversations Tab -->
            <div class="tab-content active" id="conversationsTab">
                <div class="conversations-list" id="conversationsList">
                    <div class="text-center text-muted p-3">
                        <i class="fa fa-comments fa-2x mb-2"></i>
                        <p>No conversations yet</p>
                    </div>
                </div>
            </div>

            <!-- Users Tab -->
            <div class="tab-content" id="usersTab">
                <div class="p-2">
                    <input type="text" class="form-control form-control-sm" placeholder="Search users..."
                        id="userSearch">
                </div>
                <div class="users-list" id="usersList">
                    <div class="text-center text-muted p-3">
                        <i class="fa fa-users fa-2x mb-2"></i>
                        <p>Loading users...</p>
                    </div>
                </div>
            </div>

            <!-- Chat Messages View -->
            <div class="tab-content" id="messagesTab">
                <div class="d-flex align-items-center p-2 border-bottom">
                    <button class="btn btn-sm me-2" id="backToChats">
                        <i class="fa fa-arrow-left"></i>
                    </button>
                    <img src="" alt="" class="user-avatar me-2" id="chatUserAvatar">
                    <div>
                        <div class="user-name" id="chatUserName"></div>
                        <div class="small text-muted" id="chatUserStatus"></div>
                    </div>
                </div>

                <div class="chat-messages" id="chatMessages">
                    <div class="text-center text-muted p-3">
                        <i class="fa fa-comment fa-2x mb-2"></i>
                        <p>Start a conversation</p>
                    </div>
                </div>

                <div class="typing-indicator" id="typingIndicator" style="display: none;">
                    <span id="typingUser"></span> is typing...
                </div>

                <div class="chat-input-area">
                    <div class="input-group">
                        <textarea class="form-control message-input" placeholder="Type a message..." id="messageInput"
                            rows="1"></textarea>
                        <div class="input-actions">
                            <button class="input-btn" id="emojiBtn" title="Emojis">
                                <i class="fa fa-smile-o"></i>
                            </button>
                            <button class="input-btn" id="fileBtn" title="Attach File">
                                <i class="fa fa-paperclip"></i>
                            </button>
                            <button class="input-btn" id="sendBtn" title="Send">
                                <i class="fa fa-paper-plane"></i>
                            </button>
                        </div>

                        <!-- Emoji Picker -->
                        <div class="emoji-picker" id="emojiPicker">
                            <div class="emoji-grid" id="emojiGrid"></div>
                        </div>
                    </div>

                    <!-- Hidden File Input -->
                    <input type="file" id="fileInput" style="display: none;"
                        accept="image/*,video/*,audio/*,.pdf,.doc,.docx,.txt">
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Include the JavaScript -->
<script>
// Set current user ID for JavaScript
window.currentUserId = <?php echo $current_user_id; ?>;

// Ensure chatbox doesn't interfere with existing dropdowns
document.addEventListener('DOMContentLoaded', function() {
    // Prevent chatbox events from bubbling up and interfering with dropdowns
    const chatContainer = document.getElementById('chatContainer');
    const chatToggle = document.getElementById('chatToggle');

    if (chatContainer) {
        chatContainer.addEventListener('click', function(e) {
            e.stopPropagation();
        });
    }

    // Allow chatToggle to work normally - removed stopPropagation that was interfering

    // Ensure Bootstrap dropdowns work normally
    if (typeof bootstrap !== 'undefined') {
        // Re-initialize any dropdowns that might have been affected
        const dropdowns = document.querySelectorAll('[data-bs-toggle="dropdown"]');
        dropdowns.forEach(function(dropdown) {
            if (!dropdown.hasAttribute('data-chatbox-processed')) {
                new bootstrap.Dropdown(dropdown);
                dropdown.setAttribute('data-chatbox-processed', 'true');
            }
        });
    }
});
</script>
<script src="chatbox/chatbox.js"></script>