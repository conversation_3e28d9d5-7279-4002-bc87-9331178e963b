-- Optimization queries for the chatbox system
-- Run these to improve performance
-- Add additional indexes for better query performance
ALTER TABLE `tbl_chat`
ADD INDEX `idx_recipient_timestamp` (`recipient_id`, `timestamp`),
    ADD INDEX `idx_sender_timestamp` (`sender_id`, `timestamp`),
    ADD INDEX `idx_conversation_lookup` (`sender_id`, `recipient_id`, `timestamp`);
-- Optimize conversations table
ALTER TABLE `tbl_conversations`
ADD INDEX `idx_participants_activity` (
        `participant_1`,
        `participant_2`,
        `last_activity`
    );
-- Optimize notifications table
ALTER TABLE `tbl_chat_notifications`
ADD INDEX `idx_user_unread` (`user_id`, `is_read`, `created_at`);
-- Optimize user status table
ALTER TABLE `tbl_user_status`
ADD INDEX `idx_status_activity` (`status`, `last_activity`);
-- Clean up old notifications (optional - run periodically)
-- DELETE FROM `tbl_chat_notifications` WHERE `created_at` < DATE_SUB(NOW(), INTERVAL 30 DAY);
-- Clean up old deleted messages (optional - run periodically)
-- DELETE FROM `tbl_chat` WHERE `is_deleted` = 1 AND `timestamp` < DATE_SUB(NOW(), INTERVAL 90 DAY);
-- Update table engines for better performance (if not already InnoDB)
ALTER TABLE `tbl_chat` ENGINE = InnoDB;
ALTER TABLE `tbl_conversations` ENGINE = InnoDB;
ALTER TABLE `tbl_chat_notifications` ENGINE = InnoDB;
ALTER TABLE `tbl_user_status` ENGINE = InnoDB;
ALTER TABLE `tbl_chat_files` ENGINE = InnoDB;
ALTER TABLE `tbl_chat_settings` ENGINE = InnoDB;
-- Analyze tables for query optimization
ANALYZE TABLE `tbl_chat`;
ANALYZE TABLE `tbl_conversations`;
ANALYZE TABLE `tbl_chat_notifications`;
ANALYZE TABLE `tbl_user_status`;
ANALYZE TABLE `tbl_chat_files`;
ANALYZE TABLE `tbl_chat_settings`;