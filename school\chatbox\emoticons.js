// Emoticon Picker for Chatbox
class EmoticonPicker {
    constructor() {
        console.log('EmoticonPicker constructor called');
        this.emoticons = {
            'smileys': {
                'name': 'Smileys & People',
                'emojis': [
                    '😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇',
                    '🙂', '🙃', '😉', '😌', '😍', '🥰', '😘', '😗', '😙', '😚',
                    '😋', '😛', '😝', '😜', '🤪', '🤨', '🧐', '🤓', '😎', '🤩',
                    '🥳', '😏', '😒', '😞', '😔', '😟', '😕', '🙁', '☹️', '😣',
                    '😖', '😫', '😩', '🥺', '😢', '😭', '😤', '😠', '😡', '🤬',
                    '🤯', '😳', '🥵', '🥶', '😱', '😨', '😰', '😥', '😓', '🤗',
                    '🤔', '🤭', '🤫', '🤥', '😶', '😐', '😑', '😬', '🙄', '😯',
                    '😦', '😧', '😮', '😲', '🥱', '😴', '🤤', '😪', '😵', '🤐',
                    '🥴', '🤢', '🤮', '🤧', '😷', '🤒', '🤕', '🤑', '🤠', '😈',
                    '👿', '👹', '👺', '🤡', '💩', '👻', '💀', '☠️', '👽', '👾'
                ]
            },
            'animals': {
                'name': 'Animals & Nature',
                'emojis': [
                    '🐶', '🐱', '🐭', '🐹', '🐰', '🦊', '🐻', '🐼', '🐨', '🐯',
                    '🦁', '🐮', '🐷', '🐽', '🐸', '🐵', '🙈', '🙉', '🙊', '🐒',
                    '🐔', '🐧', '🐦', '🐤', '🐣', '🐥', '🦆', '🦅', '🦉', '🦇',
                    '🐺', '🐗', '🐴', '🦄', '🐝', '🐛', '🦋', '🐌', '🐞', '🐜',
                    '🦟', '🦗', '🕷️', '🕸️', '🦂', '🐢', '🐍', '🦎', '🦖', '🦕',
                    '🐙', '🦑', '🦐', '🦞', '🦀', '🐡', '🐠', '🐟', '🐬', '🐳',
                    '🐋', '🦈', '🐊', '🐅', '🐆', '🦓', '🦍', '🦧', '🐘', '🦛',
                    '🦏', '🐪', '🐫', '🦒', '🦘', '🐃', '🐂', '🐄', '🐎', '🐖'
                ]
            },
            'food': {
                'name': 'Food & Drink',
                'emojis': [
                    '🍎', '🍐', '🍊', '🍋', '🍌', '🍉', '🍇', '🍓', '🫐', '🍈',
                    '🍒', '🍑', '🥭', '🍍', '🥥', '🥝', '🍅', '🍆', '🥑', '🥦',
                    '🥬', '🥒', '🌶️', '🫑', '🌽', '🥕', '🫒', '🧄', '🧅', '🥔',
                    '🍠', '🥐', '🥖', '🍞', '🥨', '🥯', '🧀', '🥚', '🍳', '🧈',
                    '🥞', '🧇', '🥓', '🥩', '🍗', '🍖', '🦴', '🌭', '🍔', '🍟',
                    '🍕', '🥪', '🥙', '🧆', '🌮', '🌯', '🫔', '🥗', '🥘', '🫕',
                    '🍝', '🍜', '🍲', '🍛', '🍣', '🍱', '🥟', '🦪', '🍤', '🍙',
                    '🍚', '🍘', '🍥', '🥠', '🥮', '🍢', '🍡', '🍧', '🍨', '🍦'
                ]
            },
            'activities': {
                'name': 'Activities',
                'emojis': [
                    '⚽', '🏀', '🏈', '⚾', '🥎', '🎾', '🏐', '🏉', '🥏', '🎱',
                    '🪀', '🏓', '🏸', '🏒', '🏑', '🥍', '🏏', '🪃', '🥅', '⛳',
                    '🪁', '🏹', '🎣', '🤿', '🥊', '🥋', '🎽', '🛹', '🛷', '⛸️',
                    '🥌', '🎿', '⛷️', '🏂', '🪂', '🏋️', '🤼', '🤸', '⛹️', '🤺',
                    '🏇', '🧘', '🏄', '🏊', '🤽', '🚣', '🧗', '🚵', '🚴', '🏆',
                    '🥇', '🥈', '🥉', '🏅', '🎖️', '🏵️', '🎗️', '🎫', '🎟️', '🎪',
                    '🤹', '🎭', '🩰', '🎨', '🎬', '🎤', '🎧', '🎼', '🎵', '🎶',
                    '🥁', '🪘', '🎹', '🥊', '🎯', '🎳', '🎮', '🎰', '🧩', '🃏'
                ]
            },
            'objects': {
                'name': 'Objects',
                'emojis': [
                    '⌚', '📱', '📲', '💻', '⌨️', '🖥️', '🖨️', '🖱️', '🖲️', '🕹️',
                    '🗜️', '💽', '💾', '💿', '📀', '📼', '📷', '📸', '📹', '🎥',
                    '📽️', '🎞️', '📞', '☎️', '📟', '📠', '📺', '📻', '🎙️', '🎚️',
                    '🎛️', '🧭', '⏱️', '⏲️', '⏰', '🕰️', '⌛', '⏳', '📡', '🔋',
                    '🔌', '💡', '🔦', '🕯️', '🪔', '🧯', '🛢️', '💸', '💵', '💴',
                    '💶', '💷', '🪙', '💰', '💳', '💎', '⚖️', '🪜', '🧰', '🔧',
                    '🔨', '⚒️', '🛠️', '⛏️', '🪓', '🪚', '🔩', '⚙️', '🪤', '🧲',
                    '🔫', '💣', '🧨', '🪓', '🔪', '🗡️', '⚔️', '🛡️', '🚬', '⚰️'
                ]
            },
            'hearts': {
                'name': 'Hearts',
                'emojis': [
                    '❤️', '🧡', '💛', '💚', '💙', '💜', '🖤', '🤍', '🤎', '💔',
                    '❣️', '💕', '💞', '💓', '💗', '💖', '💘', '💝', '💟', '♥️',
                    '💋', '💌', '💐', '🌹', '🌷', '🌺', '🌸', '🌼', '🌻', '💒'
                ]
            }
        };
        
        this.currentCategory = 'smileys';
        this.isVisible = false;
        this.targetInput = null;
        
        this.createPicker();
        this.bindEvents();
    }
    
    createPicker() {
        console.log('Creating emoticon picker...');
        const picker = document.createElement('div');
        picker.id = 'emoticon-picker';
        picker.className = 'emoticon-picker';
        picker.style.display = 'none';

        picker.innerHTML = `
            <div class="emoticon-header">
                <div class="emoticon-categories">
                    ${Object.keys(this.emoticons).map(key => `
                        <button class="category-btn ${key === this.currentCategory ? 'active' : ''}"
                                data-category="${key}" title="${this.emoticons[key].name}">
                            ${this.getCategoryIcon(key)}
                        </button>
                    `).join('')}
                </div>
            </div>
            <div class="emoticon-content">
                <div class="emoticon-grid" id="emoticon-grid">
                    ${this.renderEmoticons(this.currentCategory)}
                </div>
            </div>
        `;

        // Ensure picker has highest z-index (higher than backdrop)
        picker.style.zIndex = '1000000';
        picker.style.position = 'fixed';
        picker.style.pointerEvents = 'auto';

        document.body.appendChild(picker);
        this.picker = picker;
        console.log('Emoticon picker created and added to DOM:', picker);
    }
    
    getCategoryIcon(category) {
        const icons = {
            'smileys': '😀',
            'animals': '🐶',
            'food': '🍎',
            'activities': '⚽',
            'objects': '💻',
            'hearts': '❤️'
        };
        return icons[category] || '😀';
    }
    
    renderEmoticons(category) {
        return this.emoticons[category].emojis.map(emoji =>
            `<button type="button" class="emoticon-btn" data-emoji="${emoji}" title="${emoji}">${emoji}</button>`
        ).join('');
    }
    
    bindEvents() {
        // Category switching and emoji selection with event delegation
        this.picker.addEventListener('click', (e) => {
            console.log('Picker clicked:', e.target, 'Classes:', e.target.classList.toString());

            // Stop the event from bubbling to prevent backdrop from catching it
            e.stopPropagation();

            if (e.target.classList.contains('category-btn')) {
                e.preventDefault();
                const category = e.target.dataset.category;
                console.log('Category button clicked:', category);
                this.switchCategory(category);
            } else if (e.target.classList.contains('emoticon-btn')) {
                e.preventDefault();
                const emoji = e.target.dataset.emoji || e.target.textContent;
                console.log('Emoji button clicked:', emoji);
                this.insertEmoji(emoji);
            } else if (e.target.textContent && this.isEmoji(e.target.textContent.trim())) {
                e.preventDefault();
                // Handle direct emoji text clicks
                const emoji = e.target.textContent.trim();
                console.log('Direct emoji clicked:', emoji);
                this.insertEmoji(emoji);
            }
        });

        // Setup backdrop click handling after backdrop is created
        this.setupBackdropHandler();

        // Global click handler for closing picker (but not when clicking inside)
        document.addEventListener('click', (e) => {
            if (this.isVisible) {
                // Don't close if clicking on picker, emoji triggers, or their children
                const isPickerClick = this.picker && this.picker.contains(e.target);
                const isEmojiTrigger = e.target.closest('.emoji-trigger') || e.target.closest('.edit-emoji-btn');

                if (!isPickerClick && !isEmojiTrigger) {
                    console.log('Clicking outside picker area, hiding');
                    this.hide();
                }
            }
        });
    }

    isEmoji(text) {
        // Simple emoji detection
        const emojiRegex = /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/u;
        return emojiRegex.test(text);
    }

    setupBackdropHandler() {
        // Setup backdrop click handling when backdrop exists
        if (this.backdrop) {
            // Remove any existing listeners
            this.backdrop.removeEventListener('click', this.backdropClickHandler);

            // Create bound handler
            this.backdropClickHandler = (e) => {
                console.log('Backdrop event:', e.target, 'backdrop:', this.backdrop);
                // Only hide if clicking directly on backdrop
                if (e.target === this.backdrop) {
                    console.log('Direct backdrop click, hiding picker');
                    this.hide();
                } else {
                    console.log('Click on backdrop child, not hiding');
                }
            };

            this.backdrop.addEventListener('click', this.backdropClickHandler);
        }
    }

    switchCategory(category) {
        console.log('Switching to category:', category);
        this.currentCategory = category;

        // Update active category button
        this.picker.querySelectorAll('.category-btn').forEach(btn => {
            btn.classList.toggle('active', btn.dataset.category === category);
        });

        // Update emoticon grid
        const grid = document.getElementById('emoticon-grid');
        if (grid) {
            grid.innerHTML = this.renderEmoticons(category);
            console.log('Updated emoticon grid with', this.emoticons[category].emojis.length, 'emojis');
        } else {
            console.error('Emoticon grid not found');
        }
    }
    
    show(targetInput, position) {
        this.targetInput = targetInput;
        this.isVisible = true;

        // Create backdrop if it doesn't exist
        if (!this.backdrop) {
            this.backdrop = document.createElement('div');
            this.backdrop.className = 'emoji-picker-backdrop';
            this.backdrop.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0,0,0,0.1);
                z-index: 999999;
                display: none;
                pointer-events: auto;
            `;
            // Don't add click handler here - we'll handle it in setupBackdropHandler
            document.body.appendChild(this.backdrop);

            // Setup backdrop handler after creation
            this.setupBackdropHandler();
        }

        // Show backdrop and picker
        this.backdrop.style.display = 'block';
        this.picker.style.display = 'block';

        // Position the picker
        if (position) {
            // Make sure picker is visible first to get correct dimensions
            this.picker.style.visibility = 'hidden';
            this.picker.style.display = 'block';

            const pickerHeight = this.picker.offsetHeight;
            const pickerWidth = this.picker.offsetWidth;

            // Position above the button, but ensure it stays within viewport
            let top = position.y - pickerHeight - 10;
            let left = position.x - (pickerWidth / 2);

            // Adjust if picker would go off screen
            if (top < 10) {
                top = position.y + 40; // Show below button instead
            }
            if (left < 10) {
                left = 10;
            }
            if (left + pickerWidth > window.innerWidth - 10) {
                left = window.innerWidth - pickerWidth - 10;
            }

            this.picker.style.left = left + 'px';
            this.picker.style.top = top + 'px';
            this.picker.style.visibility = 'visible';
        }

        console.log('Emoji picker shown at position:', position);
    }
    
    hide() {
        this.isVisible = false;
        this.picker.style.display = 'none';
        if (this.backdrop) {
            this.backdrop.style.display = 'none';
        }
        this.targetInput = null;
    }
    
    insertEmoji(emoji) {
        console.log('Inserting emoji:', emoji, 'into:', this.targetInput);

        if (this.targetInput) {
            try {
                // Focus the input first
                this.targetInput.focus();

                const cursorPos = this.targetInput.selectionStart || 0;
                const textBefore = this.targetInput.value.substring(0, cursorPos);
                const textAfter = this.targetInput.value.substring(this.targetInput.selectionEnd || cursorPos);

                const newValue = textBefore + emoji + textAfter;
                this.targetInput.value = newValue;

                // Set cursor position after the emoji
                const newCursorPos = cursorPos + emoji.length;
                this.targetInput.setSelectionRange(newCursorPos, newCursorPos);

                // Trigger input event for any listeners
                const inputEvent = new Event('input', { bubbles: true });
                this.targetInput.dispatchEvent(inputEvent);

                console.log('Emoji inserted successfully');
            } catch (error) {
                console.error('Error inserting emoji:', error);
            }
        } else {
            console.error('No target input found for emoji insertion');
        }

        this.hide();
    }

    // Test method to verify picker functionality
    testPicker() {
        console.log('Testing emoji picker...');
        console.log('Picker element:', this.picker);
        console.log('Is visible:', this.isVisible);
        console.log('Target input:', this.targetInput);
        console.log('Backdrop:', this.backdrop);

        // Test emoji insertion
        if (this.targetInput) {
            this.insertEmoji('😀');
        }

        // Test emoji button click simulation
        const firstEmojiBtn = this.picker.querySelector('.emoticon-btn');
        if (firstEmojiBtn) {
            console.log('Found first emoji button:', firstEmojiBtn);
            console.log('Button emoji:', firstEmojiBtn.dataset.emoji);
            // Simulate click
            firstEmojiBtn.click();
        }
    }

    // Show picker without backdrop for testing
    showWithoutBackdrop(targetInput, position) {
        this.targetInput = targetInput;
        this.isVisible = true;
        this.picker.style.display = 'block';

        // Position the picker without backdrop
        if (position) {
            this.picker.style.left = position.x + 'px';
            this.picker.style.top = position.y + 'px';
        }

        console.log('Picker shown without backdrop');
    }
}

// Initialize emoticon picker when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('Initializing emoticon picker...');
    window.emoticonPicker = new EmoticonPicker();
    console.log('Emoticon picker initialized:', window.emoticonPicker);

    // Add global test functions
    window.testEmojiPickerSelection = function() {
        if (window.emoticonPicker) {
            window.emoticonPicker.testPicker();
        }
    };

    window.testEmojiPickerWithoutBackdrop = function() {
        if (window.emoticonPicker) {
            const testInput = document.getElementById('message-input') || document.getElementById('test-input');
            if (testInput) {
                window.emoticonPicker.showWithoutBackdrop(testInput, { x: 300, y: 200 });
            }
        }
    };

    window.simulateEmojiClick = function() {
        if (window.emoticonPicker && window.emoticonPicker.picker) {
            const emojiBtn = window.emoticonPicker.picker.querySelector('.emoticon-btn');
            if (emojiBtn) {
                console.log('Simulating click on:', emojiBtn);
                emojiBtn.click();
            }
        }
    };
});
