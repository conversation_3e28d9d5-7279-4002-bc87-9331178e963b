<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modern Chatbox Demo</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">
                            <i class="fas fa-comments me-2"></i>
                            Modern Chatbox Demo
                        </h4>
                    </div>
                    <div class="card-body">
                        <h5>🚀 Features</h5>
                        <ul class="list-group list-group-flush mb-4">
                            <li class="list-group-item">
                                <i class="fas fa-bolt text-warning me-2"></i>
                                <strong>Real-time messaging</strong> - Messages appear instantly
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-smile text-success me-2"></i>
                                <strong>Emoji support</strong> - Express yourself with emojis
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-paperclip text-info me-2"></i>
                                <strong>File attachments</strong> - Share images, documents, and more
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-eye text-primary me-2"></i>
                                <strong>Online status</strong> - See who's online, away, or offline
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-keyboard text-secondary me-2"></i>
                                <strong>Typing indicators</strong> - Know when someone is typing
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-mobile-alt text-danger me-2"></i>
                                <strong>Mobile responsive</strong> - Works perfectly on all devices
                            </li>
                        </ul>

                        <h5>📋 Setup Instructions</h5>
                        <div class="alert alert-info">
                            <h6><i class="fas fa-database me-2"></i>1. Database Setup</h6>
                            <p class="mb-2">Run the SQL schema to create the required tables:</p>
                            <code>mysql -u username -p database_name < chatbox/database_schema.sql</code>
                        </div>

                        <div class="alert alert-success">
                            <h6><i class="fas fa-code me-2"></i>2. Include in Your Pages</h6>
                            <p class="mb-2">Add this line to any page where you want the chatbox:</p>
                            <code>&lt;?php include 'chatbox/include_chatbox.php'; ?&gt;</code>
                        </div>

                        <div class="alert alert-warning">
                            <h6><i class="fas fa-cog me-2"></i>3. Configuration</h6>
                            <p class="mb-0">Make sure your session variables are set correctly in the API file.</p>
                        </div>

                        <h5>🎯 How to Use</h5>
                        <ol>
                            <li>Click the <strong>chat button</strong> in the bottom-right corner</li>
                            <li>Go to the <strong>Users tab</strong> to find people to chat with</li>
                            <li>Click on a user to start a conversation</li>
                            <li>Type your message and press <strong>Enter</strong> or click <strong>Send</strong></li>
                            <li>Use the <strong>emoji button</strong> to add emojis</li>
                            <li>Use the <strong>paperclip button</strong> to attach files</li>
                        </ol>

                        <h5>🔧 API Endpoints</h5>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Endpoint</th>
                                        <th>Method</th>
                                        <th>Description</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td><code>api.php?action=get_conversations</code></td>
                                        <td>GET</td>
                                        <td>Get user's conversations</td>
                                    </tr>
                                    <tr>
                                        <td><code>api.php?action=get_messages</code></td>
                                        <td>GET</td>
                                        <td>Get messages for a conversation</td>
                                    </tr>
                                    <tr>
                                        <td><code>api.php?action=send_message</code></td>
                                        <td>POST</td>
                                        <td>Send a new message</td>
                                    </tr>
                                    <tr>
                                        <td><code>api.php?action=upload_file</code></td>
                                        <td>POST</td>
                                        <td>Upload and send a file</td>
                                    </tr>
                                    <tr>
                                        <td><code>api.php?action=get_users</code></td>
                                        <td>GET</td>
                                        <td>Get list of users</td>
                                    </tr>
                                    <tr>
                                        <td><code>api.php?action=get_new_messages</code></td>
                                        <td>GET</td>
                                        <td>Poll for new messages</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="text-center mt-4">
                            <button class="btn btn-primary btn-lg" onclick="document.getElementById('chatToggle').click()">
                                <i class="fas fa-comments me-2"></i>
                                Try the Chatbox Now!
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <?php
    // Simulate a logged-in user for demo
    session_start();
    if (!isset($_SESSION['id'])) {
        $_SESSION['id'] = 1; // Demo user ID
    }
    
    // Include the chatbox
    include 'include_chatbox.php';
    ?>
</body>
</html>
