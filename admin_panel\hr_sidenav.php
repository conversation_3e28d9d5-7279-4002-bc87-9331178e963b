<!-- Vertical navbar -->
<div class="pt-4 vertical-nav bg-white margn-t" id="sidebar">

    <p class="text-gray font-weight-bold text-uppercase px-3 small pb-4 mb-0">Main</p>

    <ul class="nav flex-column bg-white mb-0">
        <?php if(in_array($_SESSION['usertype'], [8])){ ?>
        <li class="nav-item" style="background-color: #DCF8F3;">
            <a href="#" class="nav-link text-dark font-italic">
                <i class="fa fa-tasks mr-3 text-primary fa-fw"></i>
                <?php echo $hr_section; ?>
            </a>
        </li>
        <?php } ?>
        <?php 
    // Check if current user should see admin navigation
    if(in_array($_SESSION['usertype'], [1, 3, 4, 6, 10, 14])){
        $query = "SELECT * FROM tbl_system_admin WHERE `usertype` = 10 OR `usertype` = 1 ";
        $query_run = mysqli_query($connection, $query);

        $user_found = false;
        while ($row = mysqli_fetch_assoc($query_run)) {
            if ($row['empnum'] == $_SESSION['empnum']) {
                $user_found = true;
                break;
            }
        }

        // Display navigation if user is found in system admin table OR has appropriate usertype
        if($user_found || in_array($_SESSION['usertype'], [1, 3, 4, 6, 7, 14])){
    ?>
        <?php } } ?>

        <!-- Dashboard button - Always visible for appropriate user types -->
        <?php if(in_array($_SESSION['usertype'], [1, 3, 4, 6, 7, 14])){ ?>
        <li class="nav-item">
            <a href="admin_panel.php" target="_SELF" class="nav-link text-dark font-italic">
                <i class="fa fa-dashboard mr-3 text-primary fa-fw"></i>
                <?php echo $dash; ?>
            </a>
        </li>
        <?php } ?>
        <?php if(in_array($_SESSION['usertype'], [4, 5])){ ?>
        <li class="nav-item">
            <a href="admin/" class="nav-link text-dark font-italic">
                <i class="fa fa-university mr-3 text-primary fa-fw"></i>
                <?php echo 'Admin Section' ?>
            </a>
        </li>
        <?php } ?>
        <?php if(!in_array($_SESSION['usertype'], [5, 7, 9, 10, 11, 13, 15])){ ?>
        <li class="nav-item">
            <a href="tasks.php" target="_SELF" class="nav-link text-dark font-italic"
                style="background-color: #DCF8F3;">
                <i class="fa fa-tasks mr-3 text-primary fa-fw"></i>
                <?php echo $hr_section; ?>
                <?php
                    $school = $_SESSION['station'];
                    if($_SESSION['usertype']===1){
                    $query = "SELECT * FROM tbl_leave_application WHERE leave_status = 0 AND station = '".$school."' ORDER BY id DESC";
                    if($query_run = mysqli_query($connection,$query)){
                        $rowcount = mysqli_num_rows($query_run);
                        if($rowcount == 0){
                            echo '';
                        }else{
                            echo '<div style="font-size:12px;text-align:center;border-radius:50%;height:15px; width:15px;" class="bg-danger float-end">
                                <strong><span class="text-light">'.$rowcount.'</span>
                                </div>
                                </strong>
                                ';
                        }
                                
                    }
                }
                ?>
            </a>
        </li>
        <?php } ?>
        <?php 
    // Check if current user should see admin navigation
    if(in_array($_SESSION['usertype'], [1, 3, 4, 6, 10, 14])){
        $query = "SELECT * FROM tbl_system_admin WHERE `usertype` = 10 OR `usertype` = 1 ";
        $query_run = mysqli_query($connection, $query);

        $user_found = false;
        while ($row = mysqli_fetch_assoc($query_run)) {
            if ($row['empnum'] == $_SESSION['empnum']) {
                $user_found = true;
                break;
            }
        }

        // Display navigation if user is found in system admin table OR has appropriate usertype
        if($user_found || in_array($_SESSION['usertype'], [1, 3, 4, 6, 14])){
    ?>

        <li class="nav-item">
            <a href="summary/" class="nav-link text-dark font-italic">
                <i class="fa fa-cubes mr-3 text-primary fa-fw"></i>
                <?php echo $about; ?>
            </a>
        </li>
        <li class="nav-item">
            <a href="landd/" class="nav-link text-dark font-italic">
                <i class="fa fa-th-large mr-3 text-primary fa-fw"></i>
                <?php echo $LandD; ?>
            </a>
        </li>

        <?php
                    if(in_array($_SESSION['usertype'], [3, 4, 6, 14])){ ?>
        <li class="nav-item">
            <a href="item/index.php" class="nav-link text-dark font-italic">
                <i class="fa fa-list-ol mr-3 text-primary fa-fw"></i>
                <?= $items ?>
            </a>
        </li>
        <?php } ?>

        <?php
        } // Close the main if condition
    } // Close the usertype check
    ?>
        <?php
if(in_array($_SESSION['usertype'],[4, 7, 9]) OR $_SESSION['empnum'] == '4690834'){
?>
        <li class="nav-item">
            <span class="nav-link text-dark font-italic" onclick="myAccFunc()" style="cursor:pointer;">
                <i class="fa fa-files-o mr-3 text-primary fa-fw"></i>
                <?php echo $pages ?> <i class="fa fa-caret-down float-end" aria-hidden="true"></i>
            </span>
            <div id="openPage" class="w3-hide w3-white w3-card">
                <ul class="nav flex-column bg-white mb-0">
                    <li class="nav-item" style="padding-left: 20px;">
                        <a href="pages.php" class="nav-link text-dark font-italic">
                            <?php echo $login ?></a>
                    </li>
                    <li class="nav-item" style="padding-left: 20px;">
                        <a href="user_accounts.php" class="nav-link text-dark font-italic">
                            <?php echo $userAccounts ?></a>
                    </li>
                    <li class="nav-item" style="padding-left: 20px;">
                        <a href="policy.php" class="nav-link text-dark font-italic">
                            <?php echo $policy ?></a>
                    </li>
                </ul>
            </div>
        </li><?php } ; ?>
    </ul>
    <script>
    function myAccFunc() {
        var x = document.getElementById("openPage");
        if (x.className.indexOf("w3-show") == -1) {
            x.className += " w3-show";
            x.previousElementSibling.style.backgroundColor = "#DCF8F3";
        } else {
            x.className = x.className.replace(" w3-show", "");
            x.previousElementSibling.style.backgroundColor = "";
        }
    }
    </script>

    <?php 
if($_SESSION['usertype']==1){
    echo '<p class="text-gray font-weight-bold text-uppercase px-3 small py-4 mb-0">School Data</p>
    <ul class="nav flex-column bg-white mb-0">
    <li class="nav-item">
        <a href="#" class="nav-link text-dark font-italic">
            <i class="fa fa-area-chart mr-3 text-primary fa-fw"></i>
            Faculty
        </a>
    </li>
    <li class="nav-item">
        <a href="#" class="nav-link text-dark font-italic">
            <i class="fa fa-bar-chart mr-3 text-primary fa-fw"></i>
            Enrollment
        </a>
    </li>
</ul>
';
}else{
    echo '<p class="text-gray font-weight-bold text-uppercase px-3 small py-4 mb-0">Division Data</p>
    <ul class="nav flex-column bg-white mb-0">
    <li class="nav-item">
        <a href="#" class="nav-link text-dark font-italic">
            <i class="fa fa-area-chart mr-3 text-primary fa-fw"></i>
            School Heads
        </a>
    </li>
    <li class="nav-item">
        <a href="#" class="nav-link text-dark font-italic">
            <i class="fa fa-bar-chart mr-3 text-primary fa-fw"></i>
            Total Enrollment
        </a>
    </li>
</ul>
';
}
?>
    <div class="footer" style="background-color: #e6e2e2;">
        <footer class="m-3">
            <div class="small">Logged in as:</div>
            <?php
            $query = "SELECT * FROM tbl_users WHERE empnum = '".$_SESSION['empnum']."' AND usertype = '".$_SESSION['usertype']."' ";
            $query_run = mysqli_query($connection,$query);
            if($query_run){switch ($_SESSION['usertype']) {
                case 1:
                    echo 'School Admin';
                    break;
                case 2:
                    echo 'OSDS Staff';
                    break;
                case 3:
                    echo 'HR Admin';
                    break;
                case 4:
                    echo 'System Admin';
                    break;
                case 5:
                    echo 'Admin';
                    break;
                case 6:
                    echo 'Superintendent';
                    break;
                case 7:
                    echo 'User Administrator';
                    break;
                case 8:
                    echo 'HR Assistant';
                    break;
                case 9:
                    echo 'Assistant User Administrator';
                    break;
                case 10:
                    echo 'OSDS Staff';
                    break;
                case 11:
                    echo 'Admin Assistant';
                    break;
                case 12:
                    echo 'Records Admin';
                    break;
                case 13:
                    echo 'Records Assistant';
                    break;
                case 14:
                    echo 'ASDS';
                    break;
                case 15:
                    echo 'ASDS Staff';
                    break;
                }
            }
            ?>
        </footer>
    </div>
</div>
<!-- End vertical navbar -->


<!-- Page content holder -->
<div class="page-content" id="content">
    <?php if($_SESSION['usertype']===1){ ?>
    <!-- SCHOOL HEADER -->
    <?php include('hr_school_header.php') ?>
    <!-- End of SCHOOL HEADER -->
    <?php };
// Dashboard content
include('hr_contents.php');
// End Dashboard content
?>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const sidebarCollapse = document.getElementById('sidebarCollapse');
    if (sidebarCollapse) {
        sidebarCollapse.addEventListener('click', function(e) {
            e.preventDefault();
            document.getElementById('sidebar').classList.toggle('active');
            document.getElementById('content').classList.toggle('active');

            // Store state in localStorage
            const isActive = document.getElementById('sidebar').classList.contains('active');
            localStorage.setItem('sb|sidebar-toggle', isActive);
        });

        // Check for saved state
        const savedState = localStorage.getItem('sb|sidebar-toggle') === 'true';
        if (savedState) {
            document.getElementById('sidebar').classList.add('active');
            document.getElementById('content').classList.add('active');
        }
    }
});
</script>