<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Simple Message Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #f8f9fa;
            font-family: Arial, sans-serif;
        }
        
        .info-box {
            background: #17a2b8;
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .test-panel {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.9);
            color: white;
            padding: 20px;
            border-radius: 8px;
            font-size: 12px;
            z-index: 10000;
            max-width: 300px;
        }
        
        .content-area {
            height: 100vh;
            background: linear-gradient(to bottom, #e3f2fd, #bbdefb);
            padding: 20px;
            border-radius: 8px;
        }
        
        .big-button {
            position: fixed;
            bottom: 100px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 10000;
            padding: 20px 40px;
            font-size: 18px;
        }
    </style>
</head>
<body>
    <div class="test-panel" id="test-panel">
        <div><strong>📊 Message Test Status</strong></div>
        <div>Chat Open: <span id="chat-status">No</span></div>
        <div>Messages: <span id="message-count">0</span></div>
        <div>Last Action: <span id="last-action">None</span></div>
        <div style="margin-top: 10px; padding-top: 10px; border-top: 1px solid #666;">
            <div>Instructions:</div>
            <div style="font-size: 10px;">
                1. Click big button<br>
                2. Chat opens automatically<br>
                3. Type and send message<br>
                4. Check if it appears
            </div>
        </div>
    </div>
    
    <button class="btn btn-primary btn-lg big-button" onclick="startTest()">
        <i class="fa fa-play"></i><br>
        START MESSAGE TEST
    </button>

    <div style="position: fixed; bottom: 20px; right: 20px; z-index: 10000;">
        <button class="btn btn-info btn-sm" onclick="debugChatState()" style="margin: 2px;">
            <i class="fa fa-bug"></i> Debug
        </button>
        <button class="btn btn-warning btn-sm" onclick="forceLoadMessages()" style="margin: 2px;">
            <i class="fa fa-sync"></i> Force Load
        </button>
    </div>
    
    <div class="info-box">
        <h2><i class="fa fa-comments"></i> Simple Message Test</h2>
        <h4>Test Real-time Message Display</h4>
        <p><strong>Goal:</strong> Send a message and see if it appears immediately</p>
        <p><strong>Method:</strong> Use normal chat interface, not API calls</p>
    </div>
    
    <div class="content-area">
        <h2>How to Test Real-time Messages</h2>
        
        <div class="alert alert-info">
            <h5><i class="fa fa-info-circle"></i> Simple Test Process</h5>
            <ol>
                <li><strong>Click "START MESSAGE TEST"</strong> - Opens chat automatically</li>
                <li><strong>Look for a conversation</strong> - Should show existing conversations</li>
                <li><strong>Click on any conversation</strong> - Opens that chat</li>
                <li><strong>Type a message</strong> - Use the input field at bottom</li>
                <li><strong>Send the message</strong> - Press Enter or click send button</li>
                <li><strong>Check if message appears</strong> - Should show immediately</li>
            </ol>
        </div>
        
        <div class="alert alert-warning">
            <h5><i class="fa fa-exclamation-triangle"></i> What to Look For</h5>
            <ul>
                <li><strong>Message appears immediately:</strong> ✅ Working correctly</li>
                <li><strong>Message doesn't appear:</strong> ❌ Real-time issue</li>
                <li><strong>Need to close/reopen chat:</strong> ❌ Refresh issue</li>
                <li><strong>Message appears after delay:</strong> ⚠️ Polling working but send not</li>
            </ul>
        </div>
        
        <h3>Expected Behavior:</h3>
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h6>✅ Working Correctly</h6>
                    </div>
                    <div class="card-body">
                        <ul>
                            <li>Type message in input</li>
                            <li>Press Enter or click Send</li>
                            <li>Message appears immediately</li>
                            <li>Input field clears</li>
                            <li>Chat scrolls to bottom</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-danger text-white">
                        <h6>❌ Not Working</h6>
                    </div>
                    <div class="card-body">
                        <ul>
                            <li>Message doesn't appear</li>
                            <li>Input doesn't clear</li>
                            <li>Need to close/reopen chat</li>
                            <li>Message appears only after refresh</li>
                            <li>Console shows errors</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <h3>Troubleshooting:</h3>
        <div class="alert alert-secondary">
            <h6><i class="fa fa-wrench"></i> If Messages Don't Appear</h6>
            <ol>
                <li><strong>Check browser console</strong> (F12) for JavaScript errors</li>
                <li><strong>Try different conversation</strong> - might be user-specific issue</li>
                <li><strong>Refresh page</strong> and try again</li>
                <li><strong>Check network tab</strong> to see if API calls are successful</li>
                <li><strong>Try on different device/browser</strong> to isolate the issue</li>
            </ol>
        </div>
        
        <div class="alert alert-success">
            <h6><i class="fa fa-lightbulb"></i> This Test is Better Because</h6>
            <p>Instead of trying to simulate API calls, this test uses the actual chat interface exactly as users would. This gives us a real-world test of the message sending and display functionality.</p>
        </div>
    </div>
    
    <!-- Include the chatbox -->
    <?php include 'chatbox.php'; ?>
    
    <!-- Include scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="chatbox.js"></script>
    
    <script>
        function updateStatus() {
            const chatPanel = document.getElementById('chat-panel');
            const chatMessages = document.getElementById('chat-messages');
            
            const isOpen = chatPanel ? chatPanel.classList.contains('open') : false;
            const messageCount = chatMessages ? chatMessages.children.length : 0;
            
            document.getElementById('chat-status').textContent = isOpen ? 'Yes' : 'No';
            document.getElementById('message-count').textContent = messageCount;
        }
        
        function setLastAction(action) {
            document.getElementById('last-action').textContent = action;
            console.log('📝 Action:', action);
        }
        
        function startTest() {
            setLastAction('Starting test...');

            // Open chat
            const chatToggle = document.getElementById('chat-toggle');
            if (chatToggle) {
                chatToggle.click();
                setLastAction('Chat opened');

                setTimeout(() => {
                    updateStatus();

                    // Check if conversations are loaded
                    const conversationsList = document.querySelector('.conversations-list, #conversations-list');
                    if (conversationsList && conversationsList.children.length > 0) {
                        setLastAction(`Found ${conversationsList.children.length} conversations`);

                        // Auto-click the first conversation to load messages
                        const firstConversation = conversationsList.querySelector('.conversation-item, [data-user-id]');
                        if (firstConversation) {
                            setLastAction('Auto-opening first conversation...');
                            firstConversation.click();

                            setTimeout(() => {
                                const messagesContainer = document.getElementById('chat-messages');
                                const messageCount = messagesContainer ? messagesContainer.children.length : 0;
                                setLastAction(`Loaded ${messageCount} messages`);

                                // Show instructions
                                alert(`✅ Chat opened and conversation loaded!

Status:
- Conversations found: ${conversationsList.children.length}
- Messages loaded: ${messageCount}

Next steps:
1. Type a message in the input field at bottom
2. Press Enter or click Send button
3. Check if the message appears immediately
4. Watch the status panel for updates

The chat history should now be visible above the input field.`);
                            }, 1000);
                        } else {
                            setLastAction('No conversation items found');
                            alert(`⚠️ Chat opened but no conversation items found.

This might mean:
- No previous conversations exist
- Conversations are still loading
- Different HTML structure than expected

Try manually clicking on any conversation in the chat panel.`);
                        }
                    } else {
                        setLastAction('No conversations found');
                        alert(`⚠️ Chat opened but no conversations found.

This might mean:
- You haven't chatted with anyone yet
- Conversations are still loading
- API issue loading conversations

Try refreshing the page or check if you have any existing conversations.`);
                    }
                }, 1500);
            } else {
                setLastAction('ERROR: Chat toggle not found');
                alert('❌ Error: Chat toggle button not found. Please refresh the page and try again.');
            }
        }
        
        // Monitor for message changes
        let lastMessageCount = 0;
        setInterval(() => {
            updateStatus();
            
            const currentCount = parseInt(document.getElementById('message-count').textContent);
            if (currentCount > lastMessageCount) {
                setLastAction(`New message detected! (${currentCount})`);
                lastMessageCount = currentCount;
            }
        }, 1000);
        
        // Monitor for input activity
        document.addEventListener('keypress', function(e) {
            if (e.target.id === 'message-input') {
                if (e.key === 'Enter') {
                    setLastAction('Enter pressed - sending message');
                    setTimeout(() => {
                        updateStatus();
                    }, 500);
                }
            }
        });
        
        // Monitor for button clicks
        document.addEventListener('click', function(e) {
            if (e.target.closest('.send-btn, #send-message, [onclick*="send"]')) {
                setLastAction('Send button clicked');
                setTimeout(() => {
                    updateStatus();
                }, 500);
            }
        });
        
        function debugChatState() {
            setLastAction('Debugging chat state...');

            const chatPanel = document.getElementById('chat-panel');
            const chatMessages = document.getElementById('chat-messages');
            const conversationsList = document.querySelector('.conversations-list, #conversations-list');
            const messageInput = document.getElementById('message-input');

            const debug = {
                chatPanelExists: !!chatPanel,
                chatPanelOpen: chatPanel ? chatPanel.classList.contains('open') : false,
                messagesContainerExists: !!chatMessages,
                messagesCount: chatMessages ? chatMessages.children.length : 0,
                conversationsListExists: !!conversationsList,
                conversationsCount: conversationsList ? conversationsList.children.length : 0,
                messageInputExists: !!messageInput,
                currentRecipient: window.chatbox ? window.chatbox.currentRecipientId : 'No chatbox object'
            };

            console.log('🐛 Chat Debug Info:', debug);
            setLastAction('Debug info logged to console');

            let alertMsg = `🐛 Chat Debug Info:

Chat Panel: ${debug.chatPanelExists ? 'Found' : 'Missing'}
Chat Open: ${debug.chatPanelOpen ? 'Yes' : 'No'}
Messages Container: ${debug.messagesContainerExists ? 'Found' : 'Missing'}
Messages Count: ${debug.messagesCount}
Conversations List: ${debug.conversationsListExists ? 'Found' : 'Missing'}
Conversations Count: ${debug.conversationsCount}
Message Input: ${debug.messageInputExists ? 'Found' : 'Missing'}
Current Recipient: ${debug.currentRecipient}

Check browser console (F12) for detailed info.`;

            if (debug.messagesCount === 0 && debug.conversationsCount > 0) {
                alertMsg += `

⚠️ Issue Detected:
You have conversations but no messages loaded.
Try clicking "Force Load" or manually click a conversation.`;
            }

            alert(alertMsg);
        }

        function forceLoadMessages() {
            setLastAction('Force loading messages...');

            if (window.chatbox && window.chatbox.currentRecipientId) {
                if (window.chatbox.loadMessages) {
                    window.chatbox.loadMessages(window.chatbox.currentRecipientId);
                    setLastAction(`Loading messages for recipient ${window.chatbox.currentRecipientId}`);

                    setTimeout(() => {
                        updateStatus();
                        const messageCount = document.getElementById('message-count').textContent;
                        setLastAction(`Force load complete: ${messageCount} messages`);
                        alert(`Force load complete!\n\nMessages now: ${messageCount}\n\nIf still 0, there might be an API issue.`);
                    }, 2000);
                } else {
                    setLastAction('ERROR: loadMessages function not found');
                    alert('❌ Error: loadMessages function not available');
                }
            } else {
                setLastAction('No recipient selected for force load');
                alert('⚠️ No conversation selected.\n\nPlease click on a conversation first, then try force load.');
            }
        }

        console.log('🧪 Simple message test loaded');
        console.log('Click the big button to start testing');
    </script>
</body>
</html>
