<?php
include_once 'authController.php';

if(!isset($_SESSION['empnum'])){
    header('Location: /prime-hrm/index.php');
    exit();
}

if(!isset($_SESSION['id'])){
    header('Location: /prime-hrm/index.php');
    exit();
}

include 'header.php';
include 'nav_upper.php';
include 'profile_nav.php';
?>
<!-- Modal show here for Security Questions -->
<style>
/* Only add these minimal fixes */
body {
    overflow-x: hidden;
}

.container-fluid {
    max-width: 100%;
    margin: 0;
}

/* Keep toast notifications working properly */
.toast-container {
    z-index: 1056;
}
</style>

<?php
// Check security questions status
$empnum = $_SESSION['empnum'];
$check_security = "SELECT security_1, security_2, security_3, secure_answer_1, secure_answer_2, secure_answer_3 
                  FROM tbl_security 
                  WHERE empnum = ?";
$stmt = mysqli_prepare($connection, $check_security);
mysqli_stmt_bind_param($stmt, "s", $empnum);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);
$show_modal = false;

if ($row = mysqli_fetch_assoc($result)) {
    // Check if any security fields are empty
    if (empty($row['security_1']) || empty($row['security_2']) || empty($row['security_3']) ||
        empty($row['secure_answer_1']) || empty($row['secure_answer_2']) || empty($row['secure_answer_3'])) {
        $show_modal = true;
    }
}
mysqli_stmt_close($stmt);
?>

<!-- Security Questions Modal -->
<div class="modal" id="securityQuestionsModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="securityQuestionsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-md">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="securityQuestionsModalLabel">Security Questions Setup</h5>
            </div>
            <form id="securityQuestionsForm" action="update_security_questions.php" method="POST">
                <div class="modal-body">
                    <div class="alert alert-info">
                        Please set up your personalized security questions. These will be used to verify your identity if you need to reset your password.
                    </div>
                    <div class="mb-3">
                        <label for="security1" class="form-label">Security Question 1</label>
                        <input type="text" class="form-control" id="security1" name="security1" required 
                               placeholder="Enter your security question">
                    </div>
                    <div class="mb-3">
                        <label for="answer1" class="form-label">Answer 1</label>
                        <input type="text" class="form-control" id="answer1" name="answer1" required>
                    </div>
                    <div class="mb-3">
                        <label for="security2" class="form-label">Security Question 2</label>
                        <input type="text" class="form-control" id="security2" name="security2" required 
                               placeholder="Enter your security question">
                    </div>
                    <div class="mb-3">
                        <label for="answer2" class="form-label">Answer 2</label>
                        <input type="text" class="form-control" id="answer2" name="answer2" required>
                    </div>
                    <div class="mb-3">
                        <label for="security3" class="form-label">Security Question 3</label>
                        <input type="text" class="form-control" id="security3" name="security3" required 
                               placeholder="Enter your security question">
                    </div>
                    <div class="mb-3">
                        <label for="answer3" class="form-label">Answer 3</label>
                        <input type="text" class="form-control" id="answer3" name="answer3" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn-primary">Save Security Questions</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    <?php if ($show_modal): ?>
    var securityModal = new bootstrap.Modal(document.getElementById('securityQuestionsModal'));
    securityModal.show();
    <?php endif; ?>
});
</script>

<div class="container-fluid mt-4" style="margin-bottom: 30px;">
    <?php
    $empnum = $_SESSION['empnum'];
    $notif = "show";

    if (isset($_POST['updateNotif'])) {
        $notif_qry = "DELETE FROM tbl_notification WHERE empnum = ? AND notif = ?";
        $stmt = mysqli_prepare($connection, $notif_qry);
        mysqli_stmt_bind_param($stmt, "is", $empnum, $notif);
        mysqli_stmt_execute($stmt);
        mysqli_stmt_close($stmt);
    }

    $query = "SELECT * FROM tbl_notification WHERE empnum = ? AND notif = ?";
    $stmt = mysqli_prepare($connection, $query);
    mysqli_stmt_bind_param($stmt, "is", $empnum, $notif);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);

    if ($result && mysqli_num_rows($result) > 0) {
        while ($row = mysqli_fetch_assoc($result)) {
    ?>
        <div class="toast-container position-fixed bottom-0 end-0 p-3" style="margin-bottom: 50px;">
            <div class="toast show bg-success" role="alert" aria-live="assertive" aria-atomic="true" style="width: 370px; border:none;">
                <div class="toast-header">
                    <div class="bg-success p-2" style="border-radius: 50%;">
                        <i class="fa fa-check fa-lg text-light" aria-hidden="true"></i>
                    </div>
                    <strong class="me-auto" style="margin-left: 10px;">Application submitted successfully!</strong>
                    <form action="" method="POST">
                        <input type="hidden" name="notif" value="">
                        <input type="hidden" name="track_id" value="<?php echo htmlspecialchars($row['track_id']); ?>">
                        <button type="submit" name="updateNotif" class="btn border-0 text-muted" data-bs-dismiss="toast" aria-label="Close">OK</button>
                    </form>
                </div>
            </div>
        </div>
    <?php
        }
    }
    mysqli_stmt_close($stmt);
    ?>

<?php if(isset($_SESSION['mess_age'])): ?>
    <div class="alert <?php echo $_SESSION['alert-class'];?>">
        <center>
            <?php
            echo $_SESSION['mess_age']." Welcome to your profile page, <strong>".$_SESSION['shortname']."!</strong>";
            unset($_SESSION['mess_age']);
            unset($_SESSION['alert-class']);
            ?>
        </center>
    </div>
    <?php endif; ?>

    <?php if(isset($_SESSION['mess'])): ?>
    <div class="alert <?php echo $_SESSION['alert-class'];?>">
        <center>
            <?php
            echo $_SESSION['mess'];
            unset($_SESSION['mess']);
            unset($_SESSION['alert-class']);
            ?>
        </center>
    </div>
    <?php endif; ?>

    <?php if(isset($_SESSION['error'])): ?>
    <div class="alert <?php echo $_SESSION['alert-class'];?>">
        <center>
            <?php
            echo $_SESSION['error'];
            unset($_SESSION['error']);
            unset($_SESSION['alert-class']);
            ?>
        </center>
    </div>
    <?php endif; ?>

    <div class="container-fluid">
        <?php if(isset($_SESSION['pw-updated'])): ?>
            <div class="alert <?php echo $_SESSION['alert-class']; ?>">
                <center>
                    <?php
                    echo $_SESSION['pw-updated'];
                    unset($_SESSION['pw-updated']);
                    unset($_SESSION['alert-class']);
                    ?>
                </center>
            </div>
        <?php endif; ?>
    </div>

    <!-- Enhanced Grid Layout -->
    <div class="row">
        <div class="col-md-4 mb-3">
            <?php
            include('content_info.php');
            include('content_family_bg.php');
            ?>
        </div>
        <div class="col-md-8">
            <?php
            include('content_personal_info.php');
            include('content_educ_bg.php');
            ?>
        </div>
    </div>
</div>
<!-- Next Button -->
<div class="row">
    <div class="p-4 d-grid gap-2 d-md-flex justify-content-md-end">
        <span class="text-muted">page 1 |</span>
        <a href="profile_page2.php" target="_self" style="text-decoration: none;">Next <i class="fa fa-chevron-right"
                aria-hidden="true"></i></a>
    </div>
</div>

<?php include 'footer.php'; ?>

<!-- CROP PHOTO MODAL -->
<div class="modal fade" data-backdrop="static" id="modal_crop" tabindex="-1" role="dialog" aria-labelledby="modalLabel"
    aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Profile Photo</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="img-container">
                    <div class="row">
                        <div class="col-md-8">
                            <img class="img-fluid" src="" id="sample_image" />
                        </div>
                        <div class="col-md-4">
                            <div class="preview"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <span id="crop_and_upload" class="btn btn-primary">Save</span>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
            </div>
        </div>
    </div>
</div>
<!-- END OF CROP PHOTO MODAL -->


<!-- HISTORY MODAL -->
<div class="modal fade" id="Modalhistory" tabindex="-1" aria-labelledby="requestPassResetModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h1 class="modal-title fs-5" id="requestPassResetModalLabel">Promotion History</h1>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th scope="col">Date of Original Appointment</th>
                                <th scope="col">Position</th>
                                <th scope="col">Date of Last Promotion</th>
                                <th scope="col">Promoted to</th>
                                <th scope="col">Action</th>
                            </tr>
                        </thead>
                        <tbody class="table-group-divider">
                            <?php
                            $query = "SELECT * FROM `tbl_emp_status` WHERE `empnum` = ? ";
                            $stmt = $connection->prepare($query);
                            $stmt -> bind_param("i", $_SESSION['empnum']);
                            $stmt->execute();
                            $result = $stmt->get_result();
                            while($row = $result->fetch_assoc()){
                        ?>
                            <tr>
                                <td><?= $row['orig_date_appointment']; ?><span class="btn float-end text-muted border-0"
                                        style="margin-top: -5px;" data-bs-toggle="modal"
                                        data-bs-target="#appointment_<?=$row['id'];?>"><i
                                            class="fa fa-pencil"></i></span></td>
                                <td><?= $row['orig_position']; ?><span class="btn float-end text-muted border-0"
                                        style="margin-top: -5px;" data-bs-toggle="modal"
                                        data-bs-target="#position_<?=$row['id'];?>"><i class="fa fa-pencil"></i></span>
                                </td>
                                <td><?= $row['last_date_appointment']; ?><span class="btn float-end text-muted border-0"
                                        style="margin-top: -5px;" data-bs-toggle="modal"
                                        data-bs-target="#last_appointment_<?=$row['id'];?>"><i
                                            class="fa fa-pencil"></i></span></td>
                                <td><?= $row['last_position']; ?><span class="btn float-end text-muted border-0"
                                        style="margin-top: -5px;" data-bs-toggle="modal"
                                        data-bs-target="#last_position_<?=$row['id'];?>"><i
                                            class="fa fa-pencil"></i></span></td>
                                <td style="text-align: center;">
                                    <form action="utilities/updateController.php" method="POST">
                                        <input type="hidden" name="delete_id" value="<?=$row['id'];?>">
                                        <input type="hidden" name="last_position" value="<?=$row['last_position'];?>">
                                        <button type="submit" class="btn btn-sm text-danger" name="delete_prom_btn">
                                            <i class="fa fa-times" aria-hidden="true"></i>
                                        </button>
                                    </form>
                                </td>
                            </tr>
                            <?php } ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
include 'history_employment_modal.php';
include 'history_modal.php';
include 'edit_modal.php';
?>

<script>
    $(document).ready(function () {
        var $modal = $('#modal_crop');
        var crop_image = document.getElementById('sample_image');
        var cropper;
        $('#upload_image').change(function (event) {
            var files = event.target.files;
            var done = function (url) {
                crop_image.src = url;
                $modal.modal('show');
            };
            if (files && files.length > 0) {
                reader = new FileReader();
                reader.onload = function (event) {
                    done(reader.result);
                };
                reader.readAsDataURL(files[0]);
            }
        });
        $modal.on('shown.bs.modal', function () {
            cropper = new Cropper(crop_image, {
                aspectRatio: 1,
                viewMode: 2,
                preview: '.preview'
            });
        }).on('hidden.bs.modal', function () {
            cropper.destroy();
            cropper = null;
        });

        $('#crop_and_upload').click(function () {
            canvas = cropper.getCroppedCanvas({
                width: 400,
                height: 400
            });
            canvas.toBlob(function (blob) {
                url = URL.createObjectURL(blob);
                var reader = new FileReader();
                reader.readAsDataURL(blob);
                reader.onloadend = function () {
                    var base64data = reader.result;
                    $.ajax({
                        url: 'ppController.php',
                        method: 'POST',
                        data: {
                            crop_image: base64data
                        },
                        success: function (data) {
                            $modal.modal('hide');
                            location.reload();
                            alert(
                                "You have succesfully updated a profile photo!"
                            );
                        }
                    });
                };
            });
        });
    });
</script>

<?php
date_default_timezone_set('Asia/Manila');
$school_id = $_SESSION['school_id'];
$allowed_types = array('4', '7', '9', '16');


// Single query to get both count and request details
if ($_SESSION['usertype'] == '16') {
    $check_request = "SELECT 
                    (SELECT COUNT(DISTINCT pr2.id)
                     FROM tbl_password_reset pr2
                     INNER JOIN tbl_users u2 ON pr2.empnum = u2.empnum
                     WHERE pr2.request_action = '0'
                     AND SUBSTRING_INDEX(u2.station, ' - ', 1) = ?) as request_count,
                    pr.*,
                    u.station
                    FROM tbl_password_reset pr
                    INNER JOIN tbl_users u ON pr.empnum = u.empnum
                    WHERE pr.request_action = '0'
                    AND SUBSTRING_INDEX(u.station, ' - ', 1) = ?
                    ORDER BY pr.request_time DESC LIMIT 1";

    $stmt = $connection->prepare($check_request);
    $stmt->bind_param("ss", $school_id, $school_id);
} else {
    $check_request = "SELECT *, 
                    (SELECT COUNT(*) FROM tbl_password_reset WHERE request_action = ?) as request_count
                    FROM tbl_password_reset
                    WHERE request_action = ?
                    ORDER BY request_time DESC LIMIT 1";
    
    $stmt = $connection->prepare($check_request);
    $action = '0';
    $stmt->bind_param("ii", $action, $action);
}

$stmt->execute();
$result = $stmt->get_result();
$row = $result->fetch_assoc();
$request_count = ($row !== null) ? $row['request_count'] : 0;

echo "<!-- Debug: Request Count: " . $request_count . " -->";
// Add this after the initial query
echo "<!-- Debug: Usertype: $_SESSION[usertype], Request Count: $request_count -->";


function getTimeAgo($timestamp) {
    $time_ago = strtotime($timestamp);
    $current_time = time();
    $time_difference = $current_time - $time_ago;
    
    if ($time_difference < 60) {
        return "Just now";
    } elseif ($time_difference < 3600) {
        $minutes = round($time_difference / 60);
        return ($minutes == 1) ? "1 minute ago" : $minutes . " minutes ago";
    } elseif ($time_difference < 86400) {
        $hours = round($time_difference / 3600);
        return ($hours == 1) ? "1 hour ago" : $hours . " hours ago";
    } else {
        $days = round($time_difference / 86400);
        return ($days == 1) ? "1 day ago" : $days . " days ago";
    }
}

if(($_SESSION['usertype'] == '16' && $request_count > 0) ||
   (in_array($_SESSION['usertype'], ['4', '7', '9']) && $request_count > 0)):
   if($request_count > 0 && in_array($_SESSION['usertype'], $allowed_types)): ?>

<div class="toast-container position-fixed bottom-0 start-0 p-3">
    <div id="passwordResetToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="toast-header bg-primary text-white">
            <i class="fa fa-key me-2"></i>
            <strong class="me-auto">Password Reset Request</strong>
            <small><?php echo getTimeAgo($row['request_time']); ?></small>
            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
        <div class="toast-body">
            You have <b class="text-success"><?php echo $request_count; ?> pending password reset <?php echo ($request_count == 1) ? 'request' : 'requests'; ?></b> that need your attention.
            <button type="button" class="btn btn-light btn-sm border-0" data-bs-toggle="modal" data-bs-target="#requestPassResetModal">
                view
            </button>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function () {
    if (typeof bootstrap === 'undefined') {
        console.log('Waiting for Bootstrap to load...');
        return;
    }
    
    const toastElement = document.getElementById('passwordResetToast');
    const toast = new bootstrap.Toast(toastElement, {
        autohide: false
    });
    toast.show();

    const resetModal = document.getElementById('requestPassResetModal');
    resetModal.addEventListener('show.bs.modal', function () {
        toast.hide();
    });
    resetModal.addEventListener('hidden.bs.modal', function () {
        toast.show();
    });
});
</script>

<?php
$stmt->close();
endif;
endif;
?>
<div class="modal" id="requestPassResetModal" tabindex="-1" aria-labelledby="requestPassResetModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h1 class="modal-title fs-5" id="requestPassResetModalLabel">Password Reset Request</h1>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <div class="table-responsive">
          <table class="table table-hover">
            <thead>
              <tr>
                <th>Photo</th>
                <th>Employee Number</th>
                <th>Email Address</th>
                <th>Date Requested</th>
                <th>Time Requested</th>
                <th>Action</th>
              </tr>
            </thead>
            <tbody>
            <?php
                if ($_SESSION['usertype'] == '16') {
                    $query = "SELECT pr.*, u.image, u.station, u.email
                    FROM tbl_password_reset pr
                    JOIN tbl_users u ON pr.empnum = u.empnum
                    WHERE pr.request_action = ?
                    AND u.station LIKE CONCAT(?, '%')
                    ORDER BY pr.id DESC";
                    
                    $stmt = $connection->prepare($query);
                    if ($stmt) {
                        $action = '0';
                        $stmt->bind_param("is", $action, $_SESSION['school_id']);
                        $stmt->execute();
                        $result = $stmt->get_result();
                    }
                } elseif (in_array($_SESSION['usertype'], $allowed_types)) {
                    $query = "SELECT pr.*, u.image, u.email
                            FROM tbl_password_reset pr
                            JOIN tbl_users u ON pr.empnum = u.empnum
                            WHERE pr.request_action = ?
                            ORDER BY pr.id DESC";
                    
                    $stmt = $connection->prepare($query);
                    if ($stmt) {
                        $action = '0';
                        $stmt->bind_param("i", $action);
                        $stmt->execute();
                        $result = $stmt->get_result();
                    }
                }
            
                if (isset($result) && $result !== false) {
                    while($row = $result->fetch_assoc()) {
                        ?>
                        <tr>
                            <form action="resetController.php" method="post">
                                <td>
                                    <?= (($row['image'] == '') ? 
                                        '<img src="images/profile/thumbs/profile.png" width="40px" alt="">':
                                        '<img src="images/profile/thumbs/'.$row['image'].'" class="rounded-circle" width="40px" alt="">');
                                    ?>
                                </td>
                                <td><?php echo $row['empnum']; ?></td>
                                <td><?php echo $row['email']; ?></td>
                                <td><?php echo date('F d, Y', strtotime($row['request_date'])); ?></td>
                                <td><?php echo date('h:i A', strtotime($row['request_time'])); ?></td>
                                <td><button type="submit" name="reset_password" class="btn btn-primary btn-sm">Reset</button></td>
                                <input class="form-control" type="hidden" name="request_id" value="<?php echo $row['request_id']; ?>">
                                <input class="form-control" type="hidden" name="request_code" value="<?php echo $row['request_code']; ?>">
                                <input class="form-control" type="hidden" name="empnum" value="<?php echo $row['empnum']; ?>">
                            </form>
                        </tr>
                        <?php
                    }
                }
                
                if (isset($modal_stmt) && $modal_stmt !== false) {
                    $modal_stmt->close();
                }
                ?>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
window.addEventListener('load', function() {
    if (typeof bootstrap === 'undefined') {
        console.log('Waiting for Bootstrap to load...');
        return;
    }
    
    const toastElement = document.getElementById('passwordResetToast');
    if (toastElement) {
        const toast = new bootstrap.Toast(toastElement, {
            autohide: false
        });
        toast.show();
        
        const resetModal = document.getElementById('requestPassResetModal');
        if (resetModal) {
            resetModal.addEventListener('show.bs.modal', () => toast.hide());
            resetModal.addEventListener('hidden.bs.modal', () => toast.show());
        }
    }
});
</script>

<?php
// Include Chatbox Integration
include 'chatbox_integration.php';
?>
